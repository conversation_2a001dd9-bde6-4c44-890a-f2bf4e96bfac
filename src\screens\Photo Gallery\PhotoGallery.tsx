import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  ChevronDown
} from 'lucide-react';
import Layout from '../../components/common/Layout';
import { Photo } from '../../types/events';

const PhotoGallery: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'recently' | 'all' | 'videos'>('recently');
  const [selectedImage, setSelectedImage] = useState<Photo | null>(null);
  const navigate = useNavigate();

  // Mock photo data
  const photos = [
    {
      id: 1,
      filename: "IMG_21165.HEIC",
      size: "3.4 MB",
      eventName: "Corporate Event 2024",
      uploadedOn: "5 days ago",
      resolution: "3840 x 2160",
      description: "Add an event description to this image",
      downloads: 24,
      tags: ["corporate", "business", "professional", "meeting"],
      thumbnail: "/api/placeholder/300/200"
    },
    {
      id: 2,
      filename: "IMG_21166.HEIC",
      size: "3.4 MB",
      eventName: "Wedding Photography",
      uploadedOn: "3 days ago",
      resolution: "3840 x 2160",
      description: "",
      downloads: 12,
      tags: ["wedding", "ceremony", "bride", "groom", "celebration"],
      thumbnail: "/api/placeholder/300/200"
    },
    {
      id: 3,
      filename: "IMG_21167.HEIC",
      size: "3.4 MB",
      eventName: "Birthday Party",
      uploadedOn: "1 day ago",
      resolution: "3840 x 2160",
      description: "",
      downloads: 8,
      tags: ["birthday", "party", "celebration", "family"],
      thumbnail: "/api/placeholder/300/200"
    },
    {
      id: 4,
      filename: "IMG_21168.HEIC",
      size: "3.4 MB",
      eventName: "Conference 2024",
      uploadedOn: "2 hours ago",
      resolution: "3840 x 2160",
      description: "",
      downloads: 3,
      tags: ["conference", "tech", "presentation", "networking"],
      thumbnail: "/api/placeholder/300/200"
    },
    // Add more photos to fill the grid
    ...Array(12).fill(null).map((_, index) => ({
      id: index + 5,
      filename: `IMG_2116${index + 5}.HEIC`,
      size: "3.4 MB",
      eventName: `Event ${index + 5}`,
      uploadedOn: `${index + 1} days ago`,
      resolution: "3840 x 2160",
      description: "",
      downloads: Math.floor(Math.random() * 50) + 1,
      tags: ["event", "photography", "memories"],
      thumbnail: "/api/placeholder/300/200"
    }))
  ];

  const handleImageSelect = (photo: Photo) => {
    setSelectedImage(photo);
  };

  return (
    <Layout 
      searchPlaceholder="Search here..." 
      showRightSidebar={true}
      selectedPhoto={selectedImage}
      onPhotoClose={() => setSelectedImage(null)}
      showSettingsIcon={true}
    >
      {/* Gallery Content */}
      <div className="flex-1 p-6 overflow-y-auto">
        {/* Tabs and Search/Filter Controls */}
        <div className="mb-6">
          <div className="flex justify-between items-center border-b border-gray-200">
            <div className="flex">
              <button
                onClick={() => setActiveTab('recently')}
                className={`px-4 py-2 text-sm font-medium border-b-2 ${
                  activeTab === 'recently'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Recently Added
              </button>
              <button
                onClick={() => setActiveTab('all')}
                className={`px-4 py-2 text-sm font-medium border-b-2 ml-8 ${
                  activeTab === 'all'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                All Photos
              </button>
              <button
                onClick={() => setActiveTab('videos')}
                className={`px-4 py-2 text-sm font-medium border-b-2 ml-8 ${
                  activeTab === 'videos'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Videos
              </button>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by event"
                  className="pl-9 pr-4 py-2 w-64 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>
              <div className="relative">
                <select className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="newest">Newest</option>
                  <option value="oldest">Oldest</option>
                  <option value="most-downloaded">Most Downloaded</option>
                </select>
                <ChevronDown size={16} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>
        </div>

        {/* Photo Grid */}
        <div className="grid grid-cols-4 gap-4">
          {photos.map((photo) => (
            <div
              key={photo.id}
              className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleImageSelect(photo)}
            >
              <div className="w-full h-48 bg-gray-300 flex items-center justify-center">
                {/* Visible placeholder box */}
              </div>
              <div className="p-3">
                <p className="text-sm font-medium text-gray-900 text-left">{photo.filename}</p>
                <p className="text-xs text-gray-500 text-left">{photo.size}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Layout>
  );
};

export default PhotoGallery;
