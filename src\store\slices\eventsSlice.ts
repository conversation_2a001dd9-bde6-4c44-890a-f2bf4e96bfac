import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { ApiEvent, EventPhotosResponse, JoinEventResponse, NearbyEvent } from '../../services/eventsService'
import {
  fetchAllEvents,
  fetchHomeEvents,
  fetchAttendedEvents,
  fetchCreatedEvents,
  fetchEventDetails,
  fetchEventPhotos,
  joinEvent,
  leaveEvent,
  searchEvents,
  fetchNearbyEvents,
  createEvent,
} from '../thunks/eventsThunks'

export interface EventsState {
  // Events data
  allEvents: ApiEvent[]
  homeEvents: ApiEvent[]
  attendedEvents: ApiEvent[]
  createdEvents: ApiEvent[]
  nearbyEvents: NearbyEvent[]
  searchResults: ApiEvent[]
  
  // Selected event and its details
  selectedEvent: ApiEvent | null
  selectedEventPhotos: EventPhotosResponse | null
  
  // UI state
  activeFilter: 'all' | 'available' | 'joined' | 'upcoming'
  searchTerm: string
  sortBy: string
  
  // Loading states for different operations
  loading: {
    allEvents: boolean
    homeEvents: boolean
    attendedEvents: boolean
    createdEvents: boolean
    eventDetails: boolean
    eventPhotos: boolean
    joinEvent: boolean
    leaveEvent: boolean
    createEvent: boolean
    searchEvents: boolean
    nearbyEvents: boolean
  }
  
  // Error states
  error: {
    allEvents: string | null
    homeEvents: string | null
    attendedEvents: string | null
    createdEvents: string | null
    eventDetails: string | null
    eventPhotos: string | null
    joinEvent: string | null
    leaveEvent: string | null
    createEvent: string | null
    searchEvents: string | null
    nearbyEvents: string | null
  }
  
  // Success messages
  successMessage: string | null
  
  // Last updated timestamps
  lastUpdated: {
    allEvents: string | null
    homeEvents: string | null
    attendedEvents: string | null
    createdEvents: string | null
  }
}

const initialLoadingState = {
  allEvents: false,
  homeEvents: false,
  attendedEvents: false,
  createdEvents: false,
  eventDetails: false,
  eventPhotos: false,
  joinEvent: false,
  leaveEvent: false,
  createEvent: false,
  searchEvents: false,
  nearbyEvents: false,
}

const initialErrorState = {
  allEvents: null,
  homeEvents: null,
  attendedEvents: null,
  createdEvents: null,
  eventDetails: null,
  eventPhotos: null,
  joinEvent: null,
  leaveEvent: null,
  createEvent: null,
  searchEvents: null,
  nearbyEvents: null,
}

const initialState: EventsState = {
  // Events data
  allEvents: [],
  homeEvents: [],
  attendedEvents: [],
  createdEvents: [],
  nearbyEvents: [],
  searchResults: [],
  
  // Selected event
  selectedEvent: null,
  selectedEventPhotos: null,
  
  // UI state
  activeFilter: 'all',
  searchTerm: '',
  sortBy: 'newest',
  
  // Loading states
  loading: initialLoadingState,
  
  // Error states
  error: initialErrorState,
  
  // Success message
  successMessage: null,
  
  // Last updated
  lastUpdated: {
    allEvents: null,
    homeEvents: null,
    attendedEvents: null,
    createdEvents: null,
  },
}

const eventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    // UI Actions
    setActiveFilter: (state, action: PayloadAction<'all' | 'available' | 'joined' | 'upcoming'>) => {
      state.activeFilter = action.payload
    },
    
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload
    },
    
    setSortBy: (state, action: PayloadAction<string>) => {
      state.sortBy = action.payload
    },
    
    setSelectedEvent: (state, action: PayloadAction<ApiEvent | null>) => {
      state.selectedEvent = action.payload
      // Clear photos when selecting a different event
      if (action.payload?.id !== state.selectedEvent?.id) {
        state.selectedEventPhotos = null
      }
    },
    
    // Clear actions
    clearError: (state, action: PayloadAction<keyof EventsState['error']>) => {
      state.error[action.payload] = null
    },
    
    clearAllErrors: (state) => {
      state.error = initialErrorState
    },
    
    clearSuccessMessage: (state) => {
      state.successMessage = null
    },
    
    clearSearchResults: (state) => {
      state.searchResults = []
    },
    
    // Reset state
    resetEventsState: (state) => {
      return initialState
    },
  },
  extraReducers: (builder) => {
    // Fetch All Events
    builder
      .addCase(fetchAllEvents.pending, (state) => {
        state.loading.allEvents = true
        state.error.allEvents = null
      })
      .addCase(fetchAllEvents.fulfilled, (state, action) => {
        state.loading.allEvents = false
        // Handle both array and object responses
        const payload = action.payload as any
        if (Array.isArray(payload)) {
          state.allEvents = payload
        } else if (payload && Array.isArray(payload.results)) {
          state.allEvents = payload.results
        } else if (payload && Array.isArray(payload.events)) {
          state.allEvents = payload.events
        } else {
          console.warn('Unexpected API response format:', payload)
          state.allEvents = []
        }
        state.lastUpdated.allEvents = new Date().toISOString()
      })
      .addCase(fetchAllEvents.rejected, (state, action) => {
        state.loading.allEvents = false
        state.error.allEvents = action.payload as string
      })

    // Fetch Home Events
    builder
      .addCase(fetchHomeEvents.pending, (state) => {
        state.loading.homeEvents = true
        state.error.homeEvents = null
      })
      .addCase(fetchHomeEvents.fulfilled, (state, action) => {
        state.loading.homeEvents = false
        // Handle both array and object responses
        const payload = action.payload as any
        if (Array.isArray(payload)) {
          state.homeEvents = payload
        } else if (payload && Array.isArray(payload.results)) {
          state.homeEvents = payload.results
        } else if (payload && Array.isArray(payload.events)) {
          state.homeEvents = payload.events
        } else {
          state.homeEvents = []
        }
        state.lastUpdated.homeEvents = new Date().toISOString()
      })
      .addCase(fetchHomeEvents.rejected, (state, action) => {
        state.loading.homeEvents = false
        state.error.homeEvents = action.payload as string
      })

    // Fetch Attended Events
    builder
      .addCase(fetchAttendedEvents.pending, (state) => {
        state.loading.attendedEvents = true
        state.error.attendedEvents = null
      })
      .addCase(fetchAttendedEvents.fulfilled, (state, action) => {
        state.loading.attendedEvents = false
        // Handle both array and object responses
        const payload = action.payload as any
        if (Array.isArray(payload)) {
          state.attendedEvents = payload
        } else if (payload && Array.isArray(payload.results)) {
          state.attendedEvents = payload.results
        } else if (payload && Array.isArray(payload.events)) {
          state.attendedEvents = payload.events
        } else {
          state.attendedEvents = []
        }
        state.lastUpdated.attendedEvents = new Date().toISOString()
      })
      .addCase(fetchAttendedEvents.rejected, (state, action) => {
        state.loading.attendedEvents = false
        state.error.attendedEvents = action.payload as string
      })

    // Fetch Created Events
    builder
      .addCase(fetchCreatedEvents.pending, (state) => {
        state.loading.createdEvents = true
        state.error.createdEvents = null
      })
      .addCase(fetchCreatedEvents.fulfilled, (state, action) => {
        state.loading.createdEvents = false
        // Handle both array and object responses
        const payload = action.payload as any
        if (Array.isArray(payload)) {
          state.createdEvents = payload
        } else if (payload && Array.isArray(payload.results)) {
          state.createdEvents = payload.results
        } else if (payload && Array.isArray(payload.events)) {
          state.createdEvents = payload.events
        } else {
          state.createdEvents = []
        }
        state.lastUpdated.createdEvents = new Date().toISOString()
      })
      .addCase(fetchCreatedEvents.rejected, (state, action) => {
        state.loading.createdEvents = false
        state.error.createdEvents = action.payload as string
      })

    // Fetch Event Details
    builder
      .addCase(fetchEventDetails.pending, (state) => {
        state.loading.eventDetails = true
        state.error.eventDetails = null
      })
      .addCase(fetchEventDetails.fulfilled, (state, action) => {
        state.loading.eventDetails = false
        state.selectedEvent = action.payload
      })
      .addCase(fetchEventDetails.rejected, (state, action) => {
        state.loading.eventDetails = false
        state.error.eventDetails = action.payload as string
      })

    // Fetch Event Photos
    builder
      .addCase(fetchEventPhotos.pending, (state) => {
        state.loading.eventPhotos = true
        state.error.eventPhotos = null
      })
      .addCase(fetchEventPhotos.fulfilled, (state, action) => {
        state.loading.eventPhotos = false
        state.selectedEventPhotos = action.payload
      })
      .addCase(fetchEventPhotos.rejected, (state, action) => {
        state.loading.eventPhotos = false
        state.error.eventPhotos = action.payload as string
      })

    // Join Event
    builder
      .addCase(joinEvent.pending, (state) => {
        state.loading.joinEvent = true
        state.error.joinEvent = null
      })
      .addCase(joinEvent.fulfilled, (state, action) => {
        state.loading.joinEvent = false
        state.successMessage = action.payload.response.message
        // Update the event in all relevant arrays
        const eventId = action.payload.eventId
        const updateEventAttendance = (event: ApiEvent) => {
          if (event.id === eventId) {
            return { ...event, attendance_type: 'Attended' as const }
          }
          return event
        }
        state.allEvents = state.allEvents.map(updateEventAttendance)
        state.homeEvents = state.homeEvents.map(updateEventAttendance)
      })
      .addCase(joinEvent.rejected, (state, action) => {
        state.loading.joinEvent = false
        state.error.joinEvent = action.payload as string
      })

    // Leave Event
    builder
      .addCase(leaveEvent.pending, (state) => {
        state.loading.leaveEvent = true
        state.error.leaveEvent = null
      })
      .addCase(leaveEvent.fulfilled, (state, action) => {
        state.loading.leaveEvent = false
        state.successMessage = 'Successfully left the event'
        // Remove the event from attended events
        const eventId = action.payload.eventId
        state.attendedEvents = state.attendedEvents.filter(event => event.id !== eventId)
        // Update attendance type in other arrays
        const updateEventAttendance = (event: ApiEvent) => {
          if (event.id === eventId) {
            return { ...event, attendance_type: 'Created' as const }
          }
          return event
        }
        state.allEvents = state.allEvents.map(updateEventAttendance)
        state.homeEvents = state.homeEvents.map(updateEventAttendance)
      })
      .addCase(leaveEvent.rejected, (state, action) => {
        state.loading.leaveEvent = false
        state.error.leaveEvent = action.payload as string
      })

    // Search Events
    builder
      .addCase(searchEvents.pending, (state) => {
        state.loading.searchEvents = true
        state.error.searchEvents = null
      })
      .addCase(searchEvents.fulfilled, (state, action) => {
        state.loading.searchEvents = false
        // Handle both array and object responses
        const payload = action.payload as any
        if (Array.isArray(payload)) {
          state.searchResults = payload
        } else if (payload && Array.isArray(payload.results)) {
          state.searchResults = payload.results
        } else if (payload && Array.isArray(payload.events)) {
          state.searchResults = payload.events
        } else {
          state.searchResults = []
        }
      })
      .addCase(searchEvents.rejected, (state, action) => {
        state.loading.searchEvents = false
        state.error.searchEvents = action.payload as string
      })

    // Fetch Nearby Events
    builder
      .addCase(fetchNearbyEvents.pending, (state) => {
        state.loading.nearbyEvents = true
        state.error.nearbyEvents = null
      })
      .addCase(fetchNearbyEvents.fulfilled, (state, action) => {
        state.loading.nearbyEvents = false
        state.nearbyEvents = action.payload
      })
      .addCase(fetchNearbyEvents.rejected, (state, action) => {
        state.loading.nearbyEvents = false
        state.error.nearbyEvents = action.payload as string
      })

    // Create Event
    builder
      .addCase(createEvent.pending, (state) => {
        state.loading.createEvent = true
        state.error.createEvent = null
      })
      .addCase(createEvent.fulfilled, (state, action) => {
        state.loading.createEvent = false
        state.successMessage = 'Event created successfully!'
        // Add the new event to created events
        state.createdEvents.unshift(action.payload)
        // Also add to all events
        state.allEvents.unshift(action.payload)
      })
      .addCase(createEvent.rejected, (state, action) => {
        state.loading.createEvent = false
        state.error.createEvent = action.payload as string
      })
  },
})

export const {
  setActiveFilter,
  setSearchTerm,
  setSortBy,
  setSelectedEvent,
  clearError,
  clearAllErrors,
  clearSuccessMessage,
  clearSearchResults,
  resetEventsState,
} = eventsSlice.actions

// Selectors
export const selectEvents = (state: any) => state.events
export const selectAllEvents = (state: any) => state.events.allEvents
export const selectHomeEvents = (state: any) => state.events.homeEvents
export const selectAttendedEvents = (state: any) => state.events.attendedEvents
export const selectCreatedEvents = (state: any) => state.events.createdEvents
export const selectNearbyEvents = (state: any) => state.events.nearbyEvents
export const selectSearchResults = (state: any) => state.events.searchResults
export const selectSelectedEvent = (state: any) => state.events.selectedEvent
export const selectSelectedEventPhotos = (state: any) => state.events.selectedEventPhotos
export const selectActiveFilter = (state: any) => state.events.activeFilter
export const selectSearchTerm = (state: any) => state.events.searchTerm
export const selectSortBy = (state: any) => state.events.sortBy
export const selectEventsLoading = (state: any) => state.events.loading
export const selectEventsError = (state: any) => state.events.error
export const selectEventsSuccessMessage = (state: any) => state.events.successMessage

// Computed selectors for filtered events
export const selectFilteredEvents = (state: any) => {
  const { allEvents = [], homeEvents = [], attendedEvents = [], createdEvents = [], activeFilter } = state.events || {}

  // Helper function to get computed status for an event
  const getEventStatus = (event: ApiEvent) => {
    const now = new Date();
    const eventStart = event.start_date ? new Date(event.start_date) : null;
    const eventEnd = event.end_date ? new Date(event.end_date) : null;

    switch (event.attendance_type) {
      case 'Created':
        return 'available'
      case 'Attended':
        // For attended events, determine status based on time
        if (eventStart && eventEnd) {
          if (now < eventStart) {
            return 'upcoming' // Event hasn't started yet
          } else if (now >= eventStart && now <= eventEnd) {
            return 'joined' // Event is ongoing
          } else {
            return 'completed' // Event has ended
          }
        } else if (eventStart) {
          // If no end date, assume event duration is 1 day
          const eventEndEstimate = new Date(eventStart);
          eventEndEstimate.setDate(eventEndEstimate.getDate() + 1);

          if (now < eventStart) {
            return 'upcoming'
          } else if (now >= eventStart && now <= eventEndEstimate) {
            return 'joined'
          } else {
            return 'completed'
          }
        }
        return 'joined' // Default fallback
      case 'Completed':
        return 'completed'
      default:
        return 'upcoming'
    }
  }

  switch (activeFilter) {
    case 'available':
      return allEvents.filter((event: ApiEvent) =>
        event.navigation?.action === 'SHOW_JOIN_PROMPT'
      )
    case 'joined':
      return allEvents.filter((event: ApiEvent) => {
        const status = getEventStatus(event)
        return status === 'joined'
      })
    case 'upcoming':
      return allEvents.filter((event: ApiEvent) => {
        const status = getEventStatus(event)
        return status === 'upcoming'
      })
    case 'all':
    default:
      return allEvents
  }
}

export default eventsSlice.reducer
