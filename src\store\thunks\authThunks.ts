import { createAsyncThunk } from '@reduxjs/toolkit'
import { authService, LoginRequest, SignupRequest, VerifyOTPRequest, dashboardService, DashboardData } from '../../services/authService'
import { isValidToken } from '../../utils/tokenUtils'

// Login Thunk
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: LoginRequest, { rejectWithValue }) => {
    try {
      console.log('loginUser thunk - Making API call with:', credentials)
      const response = await authService.login(credentials)
      console.log('loginUser thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('loginUser thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Signup Thunk - FIXED to handle correct response format + DEBUG
export const signupUser = createAsyncThunk(
  'auth/signupUser',
  async (userData: SignupRequest, { rejectWithValue }) => {
    try {
      console.log('signupUser thunk - Making API call with:', {
        ...userData,
        password: '***',
        confirm_password: '***'
      })
      
      const response = await authService.signup(userData)
      
      console.log('signupUser thunk - Raw API response:', response)
      
      // Let's see what fields are actually in the response
      console.log('signupUser thunk - Response keys:', Object.keys(response))
      console.log('signupUser thunk - Looking for user_id:', response.user_id)
      console.log('signupUser thunk - Looking for id:', response.id)
      console.log('signupUser thunk - Full response structure:', JSON.stringify(response, null, 2))
      
      // Backend response includes both 'id' and 'user_id', normalize to 'user_id'
      const normalizedResponse = {
        ...response,
        user_id: response.user_id || response.id // Use user_id if available, fallback to id
      }
      
      console.log('signupUser thunk - Normalized response:', normalizedResponse)
      
      return normalizedResponse
    } catch (error: any) {
      console.error('signupUser thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Verify OTP Thunk
export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ userId, otpData }: { userId: string; otpData: VerifyOTPRequest }, { rejectWithValue }) => {
    try {
      console.log('verifyOTP thunk - Making API call with userId:', userId, 'otpData:', otpData)
      const response = await authService.verifyOTP(userId, otpData)
      console.log('verifyOTP thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('verifyOTP thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Forgot Password Thunk
export const forgotPassword = createAsyncThunk(
  'auth/forgotPassword',
  async (email: string, { rejectWithValue }) => {
    try {
      console.log('forgotPassword thunk - Making API call with email:', email)
      const response = await authService.forgotPassword(email)
      console.log('forgotPassword thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('forgotPassword thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Reset Password Thunk
export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async ({ email, otp, newPassword }: { email: string; otp: string; newPassword: string }, { rejectWithValue }) => {
    try {
      const response = await authService.resetPassword(email, otp, newPassword)
      return response
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

// Social Login Thunk
export const socialLogin = createAsyncThunk(
  'auth/socialLogin',
  async ({ provider, accessToken, loginMode }: { provider: string; accessToken: string; loginMode?: 'USER' | 'PHOTOGRAPHER' }, { rejectWithValue }) => {
    try {
      const response = await authService.socialLogin(provider, accessToken, loginMode)
      return response
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

// Token Validation Thunk
export const validateToken = createAsyncThunk(
  'auth/validateToken',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as any
      const token = state.auth.tokens?.access

      if (!token) {
        return rejectWithValue('No token available')
      }

      if (!isValidToken(token)) {
        return rejectWithValue('Token is expired or invalid')
      }

      return { valid: true }
    } catch (error: any) {
      console.error('validateToken thunk - Error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Dashboard Thunk
export const fetchDashboardData = createAsyncThunk(
  'dashboard/fetchData',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as any
      const token = state.auth.tokens?.access

      console.log('fetchDashboardData thunk - Starting...')
      console.log('fetchDashboardData thunk - Token exists:', !!token)

      if (!token) {
        console.log('fetchDashboardData thunk - No token available')
        return rejectWithValue('No authentication token available')
      }

      // Validate token before making API call
      if (!isValidToken(token)) {
        console.log('fetchDashboardData thunk - Token is invalid/expired')
        return rejectWithValue('Authentication token is expired or invalid')
      }

      console.log('fetchDashboardData thunk - Making API call to:', 'http://localhost:8000/api/v1/events/photographer/dashboard/')
      console.log('fetchDashboardData thunk - Using token:', token.substring(0, 20) + '...')

      const response = await dashboardService.getPhotographerDashboard(token)
      console.log('fetchDashboardData thunk - API response received:', response)
      return response
    } catch (error: any) {
      console.error('fetchDashboardData thunk - API error:', error)
      console.error('fetchDashboardData thunk - Error details:', error.message)
      return rejectWithValue(error.message)
    }
  }
)