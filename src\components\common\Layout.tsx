import React from 'react';
import LeftSidebar from './LeftSidebar';
import TopHeader from './TopHeader';
import RightSidebar from './RightSidebar';
import EventRightSidebar from './EventRightSidebar';
import { Event, Photo } from '../../types/events';

interface LayoutProps {
  children: React.ReactNode;
  showRightSidebar?: boolean;
  selectedPhoto?: Photo | null;
  selectedEvent?: Event | null;
  onPhotoClose?: () => void;
  onEventClose?: () => void;
  searchPlaceholder?: string;
  showRoleSelector?: boolean;
  showSettingsIcon?: boolean;
  showSharedWith?: boolean;
  sidebarType?: 'photo' | 'event';
}

const Layout: React.FC<LayoutProps> = ({
  children,
  showRightSidebar = false,
  selectedPhoto = null,
  selectedEvent = null,
  onPhotoClose,
  onEventClose,
  searchPlaceholder = "Search here",
  showRoleSelector = true,
  showSettingsIcon = false,
  showSharedWith = false,
  sidebarType = 'photo'
}) => {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar */}
      <LeftSidebar />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Header */}
        <TopHeader 
          searchPlaceholder={searchPlaceholder}
          showRoleSelector={showRoleSelector}
          showSettingsIcon={showSettingsIcon}
        />

        {/* Main Content with optional Right Sidebar */}
        <main className="flex-1 overflow-hidden flex">
          {/* Main Content */}
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>

          {/* Right Sidebar - Optional */}
          {showRightSidebar && (
            sidebarType === 'event' ? (
              <EventRightSidebar
                selectedEvent={selectedEvent}
                onClose={onEventClose}
              />
            ) : (
              <RightSidebar
                selectedPhoto={selectedPhoto}
                onClose={onPhotoClose}
                showSharedWith={showSharedWith}
              />
            )
          )}
        </main>
      </div>
    </div>
  );
};

export default Layout;
