import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { X, Hash } from 'lucide-react';
import { AppDispatch } from '../../store/store';
import { selectAuth } from '../../store/slices/authSlice';
import { joinEvent, fetchAllEvents } from '../../store/thunks/eventsThunks';

interface JoinEventCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const JoinEventCodeModal: React.FC<JoinEventCodeModalProps> = ({ isOpen, onClose }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { tokens } = useSelector(selectAuth);
  const [eventCode, setEventCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  if (!isOpen) return null;

  const handleJoinEvent = async () => {
    if (!eventCode.trim()) {
      setMessage('Please enter an event code');
      return;
    }

    if (!tokens?.access) {
      setMessage('Authentication required. Please log in again.');
      return;
    }

    setIsLoading(true);
    try {
      console.log('🚀 Starting join event as photographer with event ID:', eventCode.trim());

      const response = await dispatch(joinEvent({
        eventId: eventCode.trim(),
        token: tokens.access,
        data: {} // Empty data for photographer join
      })).unwrap();

      console.log('✅ Join event successful:', response);

      setMessage(response.response.message || 'Successfully joined event as photographer!');

      // Refresh events list to show the newly joined event
      if (tokens?.access) {
        dispatch(fetchAllEvents(tokens.access));
      }

      // Close modal after 3 seconds
      setTimeout(() => {
        onClose();
        setMessage('');
        setEventCode('');
      }, 3000);

    } catch (error: any) {
      console.error('❌ Join event failed:', error);
      setMessage(error.message || 'Failed to join event. Please check the event ID and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJoinEvent();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-96 max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Hash size={16} className="text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 text-left">Join Event</h3>
              <p className="text-xs text-gray-500 text-left">Enter the event ID</p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-lg"
          >
            <X size={16} className="text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Instructions */}
          <div className="bg-blue-50 rounded-lg p-4 mb-4">
            <p className="text-sm text-blue-700">
              Enter the event ID to join as a photographer. You'll get full photo upload permissions.
            </p>
          </div>

          {/* Event ID Input */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Event ID
            </label>
            <div className="relative">
              <Hash size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={eventCode}
                onChange={(e) => setEventCode(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter event ID"
                className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                maxLength={40}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Event IDs are typically 36 characters long
            </p>
          </div>

          {/* Success/Error Message */}
          {message && (
            <div className={`p-3 rounded-lg mb-4 text-sm ${
              message.includes('Successfully') 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {message}
            </div>
          )}

          {/* Join Button */}
          <button
            onClick={handleJoinEvent}
            disabled={isLoading || !eventCode.trim() || message.includes('Successfully')}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Joining Event...
              </>
            ) : (
              <>
                <Hash size={16} />
                Join Event
              </>
            )}
          </button>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
          <p className="text-xs text-gray-500 text-center">
            Don't have an event code? Contact the event organizer for access.
          </p>
        </div>
      </div>
    </div>
  );
};

export default JoinEventCodeModal;
