import React from 'react';
import {
  Menu,
  Search,
  Bell,
  Settings
} from 'lucide-react';

interface TopHeaderProps {
  searchPlaceholder?: string;
  showRoleSelector?: boolean;
  showSettingsIcon?: boolean;
}

const TopHeader: React.FC<TopHeaderProps> = ({ 
  searchPlaceholder = "Search here", 
  showRoleSelector = true,
  showSettingsIcon = false 
}) => {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button>
            <Menu size={20} className="text-gray-600" />
          </button>
          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder={searchPlaceholder}
              className="pl-9 pr-4 py-2 w-64 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
        </div>

        <div className="flex items-center gap-6">
          {showRoleSelector && (
            <div className="relative">
              <select className="text-sm text-gray-700 bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer">
                <option value="photographer">Role: Photographer</option>
                <option value="organizer">Role: Event Organizer</option>
                <option value="attendee">Role: Attendee</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          )}
          <button className="relative">
            <Bell size={20} className="text-gray-600" />
            <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          {showSettingsIcon && (
            <button>
              <Settings size={20} className="text-gray-600" />
            </button>
          )}
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">JC</span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default TopHeader;
