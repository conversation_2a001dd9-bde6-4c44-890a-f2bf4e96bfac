// Shared Event Types for PhotoFish Application
// This file contains all event-related type definitions used across components

export interface EventCreator {
  id: string
  email: string
  first_name: string
  last_name: string
  full_name: string
  display_name: string
  user_type: 'PHOTOGRAPHER' | 'ORGANIZER' | 'BOTH'
  account_status: 'ACTIVE' | 'INACTIVE'
  is_email_verified: boolean
  profile_picture: string | null
  created_at: string
  last_login_at: string
  admin_access: boolean
}

export interface EventSubscription {
  id: number
  name: string
  price: string
  description: string
  features: {
    max_events: number
    analytics_access: boolean
    priority_support: boolean
    max_photographers: number
    max_photos_per_event: number
  }
  created_at: string
  updated_at: string
}

export interface EventPhotographer {
  id: string
  username: string
  first_name: string
  last_name: string
  email: string
}

export interface EventPermissions {
  can_manage: boolean
  can_upload: boolean
  can_view_photos: boolean
  can_delete_event: boolean
}

export interface EventUploadPermissions {
  upload_allowed: boolean
  reason: string
  days_remaining: number | null
  deadline: string | null
}

export interface EventNavigation {
  action: 'SHOW_MANAGEMENT_SCREEN' | 'SHOW_ATTENDEE_SCREEN' | 'SHOW_JOIN_PROMPT' | 'SHOW_PHOTOGRAPHER_SCREEN'
  user_role: 'CREATOR' | 'ATTENDEE' | 'PHOTOGRAPHER' | 'NON_ATTENDEE'
  permissions: EventPermissions
}

export interface EventPhoto {
  id: string
  image_url: string
  thumbnail_url: string
  uploaded_at: string
  photographer: {
    id: number
    username: string
  }
}

// Main Event interface matching API response
export interface Event {
  id: string
  creator: EventCreator
  name: string
  description: string
  subscription: EventSubscription
  is_paid_subscription: boolean
  banner_image: string
  start_date: string
  end_date: string
  location: string
  latitude: number
  longitude: number
  event_type: 'WEDDING' | 'CORPORATE' | 'SPORTS' | 'PARTY' | 'CONFERENCE' | 'EXPO' | 'OTHER'
  photographers: EventPhotographer[]
  image_price_limit: string
  allow_user_uploads: boolean
  qr_code: string | null
  created_at: string
  updated_at: string
  total_photos: number
  tagged_photos: number
  is_attending: boolean
  attendance_type: 'Created' | 'Attended' | 'Completed' | null
  visibility: 'PUBLIC' | 'PRIVATE'
  requires_jersey_number: boolean
  event_status: 'scheduled' | 'ongoing' | 'ongoing_with_buffer' | 'completed' | 'available'
  upload_permissions: EventUploadPermissions
  navigation: EventNavigation

  // Computed fields for UI compatibility
  date?: string // Computed from start_date
  time?: string // Computed from start_date
  attendees?: number // Can be computed or provided
  status?: 'available' | 'joined' | 'upcoming' | 'completed' // Computed from event_status
  bannerImage?: string // Alias for banner_image
  photosTaken?: number // Alias for total_photos
  revenue?: string // Revenue information (if available)
  revenueType?: string // Revenue type information
}

// Legacy Event interface for backward compatibility
// This matches the old hardcoded structure used in components
export interface LegacyEvent {
  id: number
  name: string
  date: string
  time: string
  location: string
  attendees: number
  status: 'available' | 'joined' | 'upcoming' | 'completed'
  description: string
  bannerImage: string
  photosTaken?: number
  revenue?: string
  revenueType?: string
}

// Photo interface for photo gallery and event photos
export interface Photo {
  id: number | string
  filename: string
  size: string
  eventName?: string
  uploadedOn?: string
  resolution?: string
  downloads?: number
  tags?: string[]
  thumbnail?: string
  image_url?: string
  thumbnail_url?: string
  photographer?: {
    id: number
    username: string
  }
}

// Utility functions to convert between API and UI formats
export const convertApiEventToUIEvent = (apiEvent: Event): Event => {
  return {
    ...apiEvent,
    // Compute UI-friendly fields
    date: apiEvent.start_date,
    time: new Date(apiEvent.start_date).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }),
    bannerImage: apiEvent.banner_image,
    photosTaken: apiEvent.total_photos,
    status: convertEventStatusToUIStatus(apiEvent.event_status),
    attendees: 0, // This would need to come from a separate API call or be included in the response
  }
}

// Convert new event_status field to UI status
export const convertEventStatusToUIStatus = (
  eventStatus: 'scheduled' | 'ongoing' | 'ongoing_with_buffer' | 'completed' | 'available'
): 'available' | 'joined' | 'upcoming' | 'completed' => {
  switch (eventStatus) {
    case 'available':
      return 'available'
    case 'scheduled':
      return 'upcoming'
    case 'ongoing':
    case 'ongoing_with_buffer':
      return 'joined'
    case 'completed':
      return 'completed'
    default:
      return 'upcoming'
  }
}

// Legacy function - kept for backward compatibility
export const convertAttendanceTypeToStatus = (
  attendanceType: 'Created' | 'Attended' | 'Completed' | null,
  startDate?: string,
  endDate?: string
): 'available' | 'joined' | 'upcoming' | 'completed' => {
  if (!attendanceType) return 'upcoming'

  const now = new Date();
  const eventStart = startDate ? new Date(startDate) : null;
  const eventEnd = endDate ? new Date(endDate) : null;

  switch (attendanceType) {
    case 'Created':
      return 'available'
    case 'Attended':
      // For attended events, determine status based on time
      if (eventStart && eventEnd) {
        if (now < eventStart) {
          return 'upcoming' // Event hasn't started yet
        } else if (now >= eventStart && now <= eventEnd) {
          return 'joined' // Event is ongoing
        } else {
          return 'completed' // Event has ended
        }
      } else if (eventStart) {
        // If no end date, assume event duration is 1 day
        const eventEndEstimate = new Date(eventStart);
        eventEndEstimate.setDate(eventEndEstimate.getDate() + 1);

        if (now < eventStart) {
          return 'upcoming'
        } else if (now >= eventStart && now <= eventEndEstimate) {
          return 'joined'
        } else {
          return 'completed'
        }
      }
      return 'joined' // Default fallback
    case 'Completed':
      return 'completed'
    default:
      return 'upcoming'
  }
}

export const convertStatusToAttendanceType = (status: 'available' | 'joined' | 'upcoming' | 'completed'): 'Created' | 'Attended' | 'Completed' => {
  switch (status) {
    case 'available':
      return 'Created'
    case 'joined':
      return 'Attended'
    case 'completed':
      return 'Completed'
    default:
      return 'Created'
  }
}

// Event filter types
export type EventFilter = 'all' | 'available' | 'joined' | 'upcoming'

// Event sort options
export type EventSortOption = 'newest' | 'oldest' | 'name' | 'date'
