import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Eye, EyeOff } from 'lucide-react';
import { AppDispatch } from '../../store/store';
import { loginUser } from '../../store/thunks/authThunks';
import { clearError, selectAuth } from '../../store/slices/authSlice';
import photofishLogo from '../../assets/images/photofish-logo.svg';

const LoginScreen: React.FC = () => {
  const navigate = useNavigate();
  // Local form state (keeping original)
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [loginAsPhotographer, setLoginAsPhotographer] = useState(false);

  // Local validation states (keeping original)
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Redux integration (NEW - added only this)
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error, isAuthenticated, showRoleSelection } = useSelector(selectAuth);

  // Clear Redux errors when component mounts (NEW)
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Handle navigation after successful authentication
  useEffect(() => {
    console.log('Login navigation effect - isAuthenticated:', isAuthenticated, 'showRoleSelection:', showRoleSelection);
    if (isAuthenticated && !isLoading) {
      if (showRoleSelection) {
        console.log('Navigating to role selection');
        navigate('/role-selection', { replace: true });
      } else {
        console.log('Login successful, showing success message');
        setShowSuccessMessage(true);
        // Navigate after showing success message
        const timer = setTimeout(() => {
          console.log('Navigating to dashboard');
          setShowSuccessMessage(false);
          navigate('/dashboard', { replace: true });
        }, 1000);

        return () => clearTimeout(timer);
      }
    }
  }, [isAuthenticated, showRoleSelection, navigate, isLoading]);

  // Original validation function (unchanged)
  const handleLogin = async () => {
    // Clear previous errors
    setEmailError('');
    setPasswordError('');
    dispatch(clearError()); // NEW - clear Redux errors

    let hasErrors = false;

    // Validate email (original validation logic)
    if (!email.trim()) {
      setEmailError('Email is required');
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Please enter a valid email address');
      hasErrors = true;
    }

    // Validate password (original validation logic)
    if (!password.trim()) {
      setPasswordError('Password is required');
      hasErrors = true;
    }

    // If all validations pass, proceed with login (MODIFIED - added Redux dispatch)
    if (!hasErrors) {
      try {
        await dispatch(loginUser({
          email: email.trim(),
          password: password.trim(),
          login_mode: loginAsPhotographer ? 'PHOTOGRAPHER' : 'USER'
        })).unwrap();
        
        // Success - let Redux handle navigation via App.tsx
      } catch (err) {
        // Error handled by Redux state
        console.error('Login failed:', err);
      }
    }
  };

  return (
    <div className="min-h-screen flex bg-gray-100">
      {/* Left Side - Login Form (ORIGINAL STYLING PRESERVED) */}
      <div className="w-1/2 flex items-center justify-center pl-12">
        <div className="w-full max-w-sm">
          {/* Logo (original) */}
          <div className="mb-8">
            <div className="flex items-center">
              <img
                src={photofishLogo}
                alt="PhotoFish Logo"
                className="h-8 w-auto"
              />
            </div>
          </div>

          {/* Log In Header (original) */}
          <h1 className="text-2xl font-bold text-gray-900 mb-6 text-left">Log in</h1>

          {/* Error Message (NEW - Redux error display) */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-300 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {showSuccessMessage && (
            <div className="mb-4 p-3 bg-green-50 border border-green-300 rounded-lg">
              <p className="text-green-600 text-sm">Login Successful</p>
            </div>
          )}

          <div className="space-y-4">
            {/* Email Field (original styling) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 text-left">
                Email Address
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading} // NEW - disable when loading
                className={`w-full px-3 py-2.5 border rounded-lg focus:ring-2 focus:ring-black focus:border-transparent outline-none text-sm ${
                  emailError ? 'border-red-500' : 'border-gray-300'
                } ${isLoading ? 'bg-gray-100 cursor-not-allowed' : ''}`} // NEW - loading styles
                placeholder="Enter your email"
              />
              {emailError && <p className="text-red-500 text-sm mt-1 text-left">{emailError}</p>}
            </div>

            {/* Password Field (original styling) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 text-left">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading} // NEW - disable when loading
                  className={`w-full px-3 py-2.5 border rounded-lg focus:ring-2 focus:ring-black focus:border-transparent outline-none pr-10 text-sm ${
                    passwordError ? 'border-red-500' : 'border-gray-300'
                  } ${isLoading ? 'bg-gray-100 cursor-not-allowed' : ''}`} // NEW - loading styles
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading} // NEW - disable when loading
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
              {passwordError && (
                <p className="text-red-500 text-sm mt-1 text-left">{passwordError}</p>
              )}
            </div>

            {/* Remember Me (original) */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="remember"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                disabled={isLoading} // NEW - disable when loading
                className="w-4 h-4 text-black border-gray-300 rounded focus:ring-black"
              />
              <label htmlFor="remember" className="ml-2 text-sm text-gray-700 text-left">
                Remember me
              </label>
            </div>

            {/* Log In Button (original styling + loading state) */}
            <button
              onClick={handleLogin}
              disabled={isLoading} // NEW - disable when loading
              className={`w-full py-2.5 rounded-lg font-medium transition-colors text-sm ${
                isLoading 
                  ? 'bg-gray-400 cursor-not-allowed text-white' 
                  : 'bg-black text-white hover:bg-gray-800'
              }`} // MODIFIED - added loading styles to original
            >
              {isLoading ? 'Signing in...' : 'Log in'} {/* NEW - loading text */}
            </button>
          </div>

          {/* Links (original styling) */}
          <div className="mt-4 text-sm text-left">
            <div className="flex items-center justify-between">
              <a href="#" className="text-black hover:underline">
                Forgot Password
              </a>
              
              {/* Login as Photographer Toggle (ORIGINAL STYLING PRESERVED) */}
              <div className="flex items-center">
                <span className="text-sm text-gray-700 mr-2">Login As Photographer?</span>
                <button
                  type="button"
                  onClick={() => setLoginAsPhotographer(!loginAsPhotographer)}
                  disabled={isLoading} // NEW - disable when loading
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 ${
                    loginAsPhotographer ? 'bg-black' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      loginAsPhotographer ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* Sign up link (original) */}
            <div className="mt-2">
              <span className="text-sm text-gray-600">
                Don't have an account?{' '}
                <Link
                  to="/signup"
                  className="text-black hover:underline"
                >
                  Sign up
                </Link>
              </span>
            </div>
          </div>

          {/* Social Login (ORIGINAL STYLING PRESERVED) */}
          <div className="mt-6">
            <button 
              disabled={isLoading} // NEW - disable when loading
              className={`w-full flex items-center justify-center py-2.5 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors ${
                isLoading ? 'cursor-not-allowed opacity-50' : ''
              }`} // NEW - loading styles
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span className="text-sm font-medium text-gray-700">Continue with Google</span>
            </button>
          </div>
        </div>
      </div>

      {/* Right Side - ORIGINAL BLACK PANEL PRESERVED */}
      <div className="w-1/2 pt-12 pr-12 pb-12">
        <div className="bg-black rounded-3xl shadow-2xl h-full flex items-center justify-center">
          <div className="text-gray-400 text-center">
            <div className="w-24 h-24 bg-gray-800 rounded-2xl mx-auto mb-8 flex items-center justify-center">
              <svg className="w-12 h-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <p className="text-xl font-medium text-white">Image Placeholder</p>
            <p className="text-gray-400 mt-2">Your content goes here</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginScreen;