const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000'

// API Request/Response Types
export interface LoginRequest {
  email: string
  password: string
  login_mode: 'USER' | 'PHOTOGRAPHER'
}

export interface SignupRequest {
  first_name: string
  last_name: string
  email: string
  password: string
  confirm_password: string // Backend expects this field
  user_type?: 'USER' | 'PHOTOGRAPHER' | 'BOTH' // Optional, defaults to USER
}

export interface VerifyOTPRequest {
  otp: string
  login_mode?: 'USER' | 'PHOTOGRAPHER'
}

// API Service Class
class AuthService {
  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${API_BASE_URL}/api/v1/users/auth${endpoint}`

    // Log the request (Flutter-style)
    console.log('\n🌐 API REQUEST:')
    console.log(`📍 ${options.method || 'GET'} ${url}`)
    if (options.body) {
      console.log('📤 Request Body:', JSON.parse(options.body as string))
    }
    console.log('⏰ Time:', new Date().toLocaleTimeString())

    const startTime = Date.now()
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    const data = await response.json()
    const duration = Date.now() - startTime

    // Log the response (Flutter-style)
    console.log('\n📡 API RESPONSE:')
    console.log(`📍 ${options.method || 'GET'} ${url}`)
    console.log(`✅ Status: ${response.status} ${response.statusText}`)
    console.log(`⏱️  Duration: ${duration}ms`)
    console.log('📥 Response Data:', data)
    console.log('─'.repeat(80))

    if (!response.ok) {
      console.log('\n❌ API ERROR:')
      console.log(`📍 ${options.method || 'GET'} ${url}`)
      console.log(`🚫 Status: ${response.status} ${response.statusText}`)
      console.log('📥 Error Data:', data)
      console.log('─'.repeat(80))
      throw new Error(data.message || data.detail || 'Something went wrong')
    }

    return data
  }

  async login(credentials: LoginRequest) {
    return this.makeRequest('/login/', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })
  }

  async signup(userData: SignupRequest) {
    // FIXED: Changed from /signup/ to /register/
    const payload = {
      ...userData,
      confirm_password: userData.password, // Backend expects confirm_password
      user_type: userData.user_type || 'USER' // Default to USER if not specified
    }
    
    return this.makeRequest('/register/', {
      method: 'POST',
      body: JSON.stringify(payload),
    })
  }

  async verifyOTP(userId: string, otpData: VerifyOTPRequest) {
    return this.makeRequest(`/verify-otp/${userId}/`, {
      method: 'POST',
      body: JSON.stringify(otpData),
    })
  }

  async forgotPassword(email: string) {
    return this.makeRequest('/forgot-password/', {
      method: 'POST',
      body: JSON.stringify({ email }),
    })
  }

  async resetPassword(email: string, otp: string, newPassword: string) {
    return this.makeRequest('/reset-password/', {
      method: 'POST',
      body: JSON.stringify({
        email,
        otp,
        new_password: newPassword
      }),
    })
  }

  async socialLogin(provider: string, accessToken: string, loginMode: 'USER' | 'PHOTOGRAPHER' = 'USER') {
    return this.makeRequest('/oauth/login/', {
      method: 'POST',
      body: JSON.stringify({
        provider,
        access_token: accessToken,
        login_mode: loginMode
      }),
    })
  }
}

export const authService = new AuthService()

// Dashboard API Service
export interface DashboardStats {
  total_images: {
    count: number
    label: string
  }
  total_events: {
    count: number
    label: string
  }
  total_revenue: {
    amount: string
    pending_disbursement: string
    label: string
  }
}

export interface QuickUpload {
  has_active_events: boolean
  primary_event: {
    id: string
    name: string
  } | null
}

export interface Notification {
  id: string
  type: string
  title: string
  message: string
  time_ago: string
}

export interface RecentEvent {
  id: string
  name: string
  date: string
  status: string
}

export interface DashboardData {
  success: boolean
  dashboard: {
    stats: DashboardStats
    quick_upload: QuickUpload
    notifications: Notification[]
    recent_events: RecentEvent[]
    nearby_events: any[]
  }
  generated_at: string
}

class DashboardService {
  private async makeRequest(endpoint: string, token: string, options: RequestInit = {}) {
    const url = `${API_BASE_URL}/api/v1/events${endpoint}`

    // Log the request (Flutter-style)
    console.log('\n🌐 DASHBOARD API REQUEST:')
    console.log(`📍 ${options.method || 'GET'} ${url}`)
    console.log('🔐 Authorization: Bearer ***' + token.substring(token.length - 10))
    if (options.body) {
      console.log('📤 Request Body:', JSON.parse(options.body as string))
    }
    console.log('⏰ Time:', new Date().toLocaleTimeString())

    const startTime = Date.now()
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
      ...options,
    })

    const data = await response.json()
    const duration = Date.now() - startTime

    // Log the response (Flutter-style)
    console.log('\n📡 DASHBOARD API RESPONSE:')
    console.log(`📍 ${options.method || 'GET'} ${url}`)
    console.log(`✅ Status: ${response.status} ${response.statusText}`)
    console.log(`⏱️  Duration: ${duration}ms`)
    console.log('📥 Response Data:', data)
    console.log('─'.repeat(80))

    if (!response.ok) {
      console.log('\n❌ DASHBOARD API ERROR:')
      console.log(`📍 ${options.method || 'GET'} ${url}`)
      console.log(`🚫 Status: ${response.status} ${response.statusText}`)
      console.log('📥 Error Data:', data)
      console.log('─'.repeat(80))
      throw new Error(data.message || data.detail || 'Something went wrong')
    }

    return data
  }

  async getPhotographerDashboard(token: string): Promise<DashboardData> {
    return this.makeRequest('/photographer/dashboard/', token, {
      method: 'GET',
    })
  }
}

export const dashboardService = new DashboardService()