import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Calendar,
  MapPin,
  Users,
  DollarSign,
  Clock,
  X,
  Camera
} from 'lucide-react';
import { Event } from '../../types/events';
import { AppDispatch } from '../../store/store';
import { selectAuth } from '../../store/slices/authSlice';
import { joinEvent, fetchAllEvents } from '../../store/thunks/eventsThunks';
import JoinEventModal from '../modals/JoinEventModal';

interface EventRightSidebarProps {
  selectedEvent: Event | null;
  onClose?: () => void;
}

const EventRightSidebar: React.FC<EventRightSidebarProps> = ({
  selectedEvent,
  onClose
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { tokens } = useSelector(selectAuth);
  const [showJoinEventModal, setShowJoinEventModal] = useState(false);

  const handleViewDetails = () => {
    if (!selectedEvent) return;

    console.log('🔍 Event details navigation:', {
      eventId: selectedEvent.id,
      eventName: selectedEvent.name,
      event_status: selectedEvent.event_status,
      status: selectedEvent.status,
      attendance_type: selectedEvent.attendance_type
    });

    // Navigate to appropriate event detail page based on event_status with event ID
    if (selectedEvent.event_status === 'completed') {
      navigate(`/event-detail-completed/${selectedEvent.id}`);
    } else if (selectedEvent.event_status === 'ongoing' || selectedEvent.event_status === 'ongoing_with_buffer') {
      navigate(`/event-detail-in-progress/${selectedEvent.id}`);
    } else if (selectedEvent.event_status === 'scheduled') {
      navigate(`/event-detail-scheduled/${selectedEvent.id}`);
    } else {
      // Fallback to old logic if event_status is not available
      console.log('⚠️ Using fallback navigation logic - event_status not available');
      if (selectedEvent.status === 'completed') {
        navigate(`/event-detail-completed/${selectedEvent.id}`);
      } else if (selectedEvent.status === 'upcoming' || selectedEvent.status === 'joined') {
        navigate(`/event-detail-in-progress/${selectedEvent.id}`);
      } else {
        navigate(`/event-detail-scheduled/${selectedEvent.id}`);
      }
    }
  };

  const handleJoinEventClick = () => {
    if (!selectedEvent) return;
    console.log('🎯 Join event button clicked for event:', selectedEvent.name, selectedEvent.id);
    setShowJoinEventModal(true);
  };

  const handleJoinEventModalClose = () => {
    console.log('🔒 Closing join event modal');
    setShowJoinEventModal(false);
    // Refresh events after modal closes (in case event was joined)
    if (tokens?.access) {
      dispatch(fetchAllEvents(tokens.access));
    }
  };

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
      {selectedEvent ? (
        <>
          {/* Header with close button and join button */}
          <div className="p-6 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Event Details</h3>
            <div className="flex items-center gap-2">

              {onClose && (
                <button
                  onClick={onClose}
                  className="p-1 hover:bg-gray-100 rounded-lg"
                >
                  <X size={16} className="text-gray-400" />
                </button>
              )}
            </div>
          </div>

          {/* Event Banner */}
          <div className="p-6 border-b border-gray-200">
            <div className="w-full h-32 bg-gray-300 rounded-lg mb-4 relative overflow-hidden">
              {selectedEvent.banner_image || selectedEvent.bannerImage ? (
                <img
                  src={selectedEvent.banner_image || selectedEvent.bannerImage}
                  alt={selectedEvent.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback to placeholder if image fails to load
                    e.currentTarget.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200">
                  <span className="text-blue-600 font-bold text-2xl">
                    {selectedEvent.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            <div className="flex items-center justify-between">
              <div className="text-left">
                <p className="text-sm font-medium text-gray-900 text-left mb-2">{selectedEvent.name}</p>
                <div className="w-full h-px bg-gray-200"></div>
              </div>
            </div>
          </div>

          {/* Event Information */}
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-sm font-semibold text-gray-900 mb-4 text-left">Event Information</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar size={16} className="text-gray-400" />
                <div className="text-left">
                  <p className="text-xs text-gray-500 text-left">Date & Time</p>
                  <p className="text-xs text-gray-900 font-medium text-left">
                    {selectedEvent.date || new Date(selectedEvent.start_date).toLocaleDateString()} at {
                      selectedEvent.time || new Date(selectedEvent.start_date).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                      })
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <MapPin size={16} className="text-gray-400" />
                <div className="text-left">
                  <p className="text-xs text-gray-500 text-left">Location</p>
                  <p className="text-xs text-gray-900 font-medium text-left">
                    {selectedEvent.location || 'Location TBD'}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Users size={16} className="text-gray-400" />
                <div className="text-left">
                  <p className="text-xs text-gray-500 text-left">Attendees</p>
                  <p className="text-xs text-gray-900 font-medium text-left">
                    {selectedEvent.attendees || (selectedEvent.photographers?.length || 0) + 1} people
                  </p>
                </div>
              </div>
              {(selectedEvent.photosTaken || selectedEvent.total_photos) && (
                <div className="flex items-center gap-3">
                  <Camera size={16} className="text-gray-400" />
                  <div className="text-left">
                    <p className="text-xs text-gray-500 text-left">Photos Taken</p>
                    <p className="text-xs text-gray-900 font-medium text-left">
                      {selectedEvent.photosTaken || selectedEvent.total_photos || 0}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Event Description */}
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-sm font-semibold text-gray-900 mb-4 text-left">Description</h3>
            <p className="text-xs text-gray-600 text-left">
              {selectedEvent.description}
            </p>
          </div>

          {/* Revenue Information (for completed events) */}
          {selectedEvent.status === 'completed' && selectedEvent.revenue && (
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-sm font-semibold text-gray-900 mb-4 text-left">Revenue</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <DollarSign size={16} className="text-green-500" />
                  <div>
                    <p className="text-xs text-gray-500">Revenue Generated</p>
                    <p className="text-xs text-gray-900 font-medium">{selectedEvent.revenue}</p>
                  </div>
                </div>
                {selectedEvent.revenueType && (
                  <div className="flex items-center gap-3">
                    <Clock size={16} className="text-gray-400" />
                    <div>
                      <p className="text-xs text-gray-500">Revenue Type</p>
                      <p className="text-xs text-gray-900 font-medium">{selectedEvent.revenueType}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="p-6 space-y-3 mt-auto">
            {/* Show Join Event button for available events */}
            {(selectedEvent.navigation?.action === 'SHOW_JOIN_PROMPT' ||
              selectedEvent.event_status === 'available' ||
              (selectedEvent.navigation?.user_role !== 'PHOTOGRAPHER' && selectedEvent.upload_permissions?.reason === 'Event is active')) ? (
              <button
                onClick={handleJoinEventClick}
                className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
              >
                Join Event
              </button>
            ) : (
              <button
                onClick={handleViewDetails}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
              >
                View Full Details
              </button>
            )}
          </div>
        </>
      ) : (
        /* No Event Selected State */
        <div className="flex-1 flex items-center justify-center p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
              <Calendar size={24} className="text-gray-400" />
            </div>
            <p className="text-sm text-gray-500">Select an event to view details</p>
          </div>
        </div>
      )}

      {/* Join Event Modal */}
      <JoinEventModal
        isOpen={showJoinEventModal}
        onClose={handleJoinEventModalClose}
        event={selectedEvent}
      />
    </div>
  );
};

export default EventRightSidebar;
