import React from 'react';
import { Calendar, DollarSign, Camera, MapPin } from 'lucide-react';

const EventDetailCompletedSimple: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Event Detail - Completed</h1>
        
        {/* Event Header Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-blue-600 font-bold text-2xl">C</span>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900 text-left">CES TECH EXPO 2025</h1>
                <span className="px-3 py-1 text-sm font-medium text-green-600 bg-green-100 rounded-full">
                  Completed
                </span>
              </div>
              <p className="text-gray-600 text-left">Create new rule for opera gallery in WebFlow from research and tests to final deliverables docs.</p>
            </div>
          </div>

          {/* Overview Tab */}
          <div className="flex gap-8 border-b border-gray-200">
            <button className="text-sm font-medium text-blue-600 border-b-2 border-blue-600 pb-2">
              Overview
            </button>
            <button className="text-sm font-medium text-gray-500 hover:text-gray-700 pb-2">
              Revenue
            </button>
          </div>
        </div>

        {/* Event Details Cards */}
        <div className="grid grid-cols-2 gap-6 mb-6">
          {/* Date Card */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Calendar size={20} className="text-blue-600" />
              </div>
            </div>
            <div className="text-left">
              <p className="text-sm text-gray-500 mb-1">Date</p>
              <p className="text-2xl font-bold text-gray-900 mb-1">Dec 12, 2022</p>
              <p className="text-xs text-gray-500">(Completed on Nov 07, 2023)</p>
            </div>
          </div>

          {/* Revenue Mode Card */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign size={20} className="text-green-600" />
              </div>
            </div>
            <div className="text-left">
              <p className="text-sm text-gray-500 mb-1">Revenue Made</p>
              <p className="text-2xl font-bold text-gray-900 mb-1">$3,632.30</p>
              <p className="text-xs text-gray-500">Pay Per Photo</p>
            </div>
          </div>

          {/* Photos Taken Card */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Camera size={20} className="text-purple-600" />
              </div>
            </div>
            <div className="text-left">
              <p className="text-sm text-gray-500 mb-1">Photos Taken</p>
              <p className="text-2xl font-bold text-gray-900">250</p>
            </div>
          </div>

          {/* Location Card */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <MapPin size={20} className="text-orange-600" />
              </div>
            </div>
            <div className="text-left">
              <p className="text-sm text-gray-500 mb-1">Location</p>
              <p className="text-lg font-bold text-gray-900">Marina Beach, Chennai, Tamil Nadu</p>
            </div>
          </div>
        </div>

        {/* Photo Gallery Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Uploaded Images</h2>
          <div className="grid grid-cols-4 gap-4">
            {[1,2,3,4,5,6,7,8,9,10,11,12].map(i => (
              <div key={i} className="bg-gray-200 aspect-square rounded-lg flex items-center justify-center">
                <span className="text-gray-500 text-sm">IMG_{21164 + i}.HEIC</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventDetailCompletedSimple;
