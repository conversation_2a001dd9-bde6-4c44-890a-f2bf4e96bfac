import { createAsyncThunk } from '@reduxjs/toolkit'
import { 
  eventsService, 
  ApiEvent, 
  CreateEventRequest, 
  JoinEventRequest, 
  JoinEventResponse,
  SearchEventsRequest,
  NearbyEventsRequest,
  NearbyEvent,
  EventPhotosResponse
} from '../../services/eventsService'

// Fetch All Events
export const fetchAllEvents = createAsyncThunk(
  'events/fetchAllEvents',
  async (token: string, { rejectWithValue }) => {
    try {
      console.log('fetchAllEvents thunk - Making API call')
      const response = await eventsService.getAllEvents(token)
      console.log('fetchAllEvents thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('fetchAllEvents thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Fetch Home Events (User's Personalized Events)
export const fetchHomeEvents = createAsyncThunk(
  'events/fetchHomeEvents',
  async (token: string, { rejectWithValue }) => {
    try {
      console.log('fetchHomeEvents thunk - Making API call')
      const response = await eventsService.getHomeEvents(token)
      console.log('fetchHomeEvents thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('fetchHomeEvents thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Fetch Attended Events
export const fetchAttendedEvents = createAsyncThunk(
  'events/fetchAttendedEvents',
  async (token: string, { rejectWithValue }) => {
    try {
      console.log('fetchAttendedEvents thunk - Making API call')
      const response = await eventsService.getAttendedEvents(token)
      console.log('fetchAttendedEvents thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('fetchAttendedEvents thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Fetch Created Events
export const fetchCreatedEvents = createAsyncThunk(
  'events/fetchCreatedEvents',
  async (token: string, { rejectWithValue }) => {
    try {
      console.log('fetchCreatedEvents thunk - Making API call')
      const response = await eventsService.getCreatedEvents(token)
      console.log('fetchCreatedEvents thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('fetchCreatedEvents thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Fetch Event Details
export const fetchEventDetails = createAsyncThunk(
  'events/fetchEventDetails',
  async ({ eventId, token }: { eventId: string; token: string }, { rejectWithValue }) => {
    try {
      console.log('fetchEventDetails thunk - Making API call for event:', eventId)
      const response = await eventsService.getEventDetails(eventId, token)
      console.log('fetchEventDetails thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('fetchEventDetails thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Fetch Event Photos
export const fetchEventPhotos = createAsyncThunk(
  'events/fetchEventPhotos',
  async ({ eventId, token }: { eventId: string; token: string }, { rejectWithValue }) => {
    try {
      console.log('fetchEventPhotos thunk - Making API call for event:', eventId)
      const response = await eventsService.getEventPhotos(eventId, token)
      console.log('fetchEventPhotos thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('fetchEventPhotos thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Join Event
export const joinEvent = createAsyncThunk(
  'events/joinEvent',
  async ({ eventId, token, data }: { eventId: string; token: string; data: JoinEventRequest }, { rejectWithValue }) => {
    try {
      console.log('🔥 joinEvent thunk - Starting API call')
      console.log('📋 Event ID:', eventId)
      console.log('🔑 Token present:', !!token)
      console.log('📦 Data:', data)

      const response = await eventsService.joinEvent(eventId, token, data)
      console.log('🎉 joinEvent thunk - API response:', response)
      return { eventId, response }
    } catch (error: any) {
      console.error('💥 joinEvent thunk - API error:', error)
      console.error('💥 Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      return rejectWithValue(error.message)
    }
  }
)

// Leave Event
export const leaveEvent = createAsyncThunk(
  'events/leaveEvent',
  async ({ eventId, token }: { eventId: string; token: string }, { rejectWithValue }) => {
    try {
      console.log('leaveEvent thunk - Making API call for event:', eventId)
      const response = await eventsService.leaveEvent(eventId, token)
      console.log('leaveEvent thunk - API response:', response)
      return { eventId, response }
    } catch (error: any) {
      console.error('leaveEvent thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Search Events
export const searchEvents = createAsyncThunk(
  'events/searchEvents',
  async ({ token, searchData }: { token: string; searchData: SearchEventsRequest }, { rejectWithValue }) => {
    try {
      console.log('searchEvents thunk - Making API call with search data:', searchData)
      const response = await eventsService.searchEvents(token, searchData)
      console.log('searchEvents thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('searchEvents thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Get Nearby Events
export const fetchNearbyEvents = createAsyncThunk(
  'events/fetchNearbyEvents',
  async ({ token, locationData }: { token: string; locationData: NearbyEventsRequest }, { rejectWithValue }) => {
    try {
      console.log('fetchNearbyEvents thunk - Making API call with location data:', locationData)
      const response = await eventsService.getNearbyEvents(token, locationData)
      console.log('fetchNearbyEvents thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('fetchNearbyEvents thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)

// Create Event
export const createEvent = createAsyncThunk(
  'events/createEvent',
  async ({ token, eventData }: { token: string; eventData: CreateEventRequest }, { rejectWithValue }) => {
    try {
      console.log('createEvent thunk - Making API call with event data:', eventData)
      const response = await eventsService.createEvent(token, eventData)
      console.log('createEvent thunk - API response:', response)
      return response
    } catch (error: any) {
      console.error('createEvent thunk - API error:', error)
      return rejectWithValue(error.message)
    }
  }
)
