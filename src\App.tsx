import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectAuth, checkTokenValidity } from './store/slices/authSlice';
import { validateToken } from './store/thunks/authThunks';
import { AppDispatch } from './store/store';
import { LoginScreen } from './screens/login';
import { SignupScreen } from './screens/signup';
import OtpScreen from './screens/otp/OtpScreen';
import RoleSelectionScreen from './screens/role-selection/RoleSelectionScreen';
import { AccountSuccessScreen } from './screens/account-success';
import PhotographerDashboard from './screens/photographer-dashboard/PhotographerDashboard';
import PhotoGallery from './screens/Photo Gallery/PhotoGallery';
import Events from './screens/Events/Events';
import EventDetailInProgress from './screens/EventDetail/EventDetailInProgress';
import EventDetailCompleted from './screens/EventDetail/EventDetailCompleted';
import EventDetailInProgressSimple from './screens/EventDetail/EventDetailInProgressSimple';
import EventDetailCompletedSimple from './screens/EventDetail/EventDetailCompletedSimple';
import EventDetailScheduled from './screens/EventDetail/EventDetailScheduled';
import './App.css';

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading, tokens } = useSelector(selectAuth);

  console.log('ProtectedRoute - isAuthenticated:', isAuthenticated, 'isLoading:', isLoading, 'hasToken:', !!tokens?.access);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // For now, just check if authenticated (temporarily remove token check)
  if (!isAuthenticated) {
    console.log('ProtectedRoute - Not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('ProtectedRoute - Allowing access to dashboard');
  return <>{children}</>;
};

// Root redirect component
const RootRedirect: React.FC = () => {
  const { isAuthenticated, tokens, isLoading } = useSelector(selectAuth);

  console.log('RootRedirect - isAuthenticated:', isAuthenticated, 'hasToken:', !!tokens?.access, 'isLoading:', isLoading);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Check if user has valid authentication
  if (isAuthenticated && tokens?.access) {
    console.log('RootRedirect - Redirecting to dashboard');
    return <Navigate to="/dashboard" replace />;
  }

  console.log('RootRedirect - Redirecting to login');
  return <Navigate to="/login" replace />;
};

// Authentication Route Component (just shows auth forms, no redirects)
const AuthRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isLoading } = useSelector(selectAuth);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Just show the auth form - let login screen handle navigation
  return <>{children}</>;
};

function App() {
  const dispatch = useDispatch<AppDispatch>();
  // Keep some state for OTP flow and user data
  const [userEmail] = useState('<EMAIL>');
  const [userName] = useState('Jack');
  const [isInitialized, setIsInitialized] = useState(false);

  // Redux state
  const { user, isAuthenticated, showRoleSelection, pendingUserId, tokens } = useSelector(selectAuth);

  // Check token validity on app startup
  useEffect(() => {
    const initializeApp = async () => {
      console.log('App initializing - checking authentication state');
      console.log('Current auth state:', { isAuthenticated, tokens: !!tokens?.access });

      // Check if we have stored tokens and validate them
      if (tokens?.access) {
        console.log('Found stored token, validating...');
        try {
          await dispatch(validateToken()).unwrap();
          console.log('Token validation successful');
        } catch (error) {
          console.log('Token validation failed:', error);
          // Token validation will clear the auth state automatically
        }
      } else {
        console.log('No stored token found');
        // Use the synchronous action to clear any inconsistent state
        dispatch(checkTokenValidity());
      }
      setIsInitialized(true);
    };

    initializeApp();
  }, [dispatch, isAuthenticated, tokens]);

  // Show loading screen while initializing
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Authentication Routes */}
          <Route path="/login" element={<LoginScreen />} />
          <Route path="/signup" element={<SignupScreen />} />

          {/* OTP Route - Special handling for pending verification */}
          <Route path="/otp" element={
            pendingUserId ? (
              <OtpScreen
                email={userEmail}
                onNavigateBack={() => {}} // Will be handled by router
                onVerifyOtp={() => {}} // Will be handled by router
              />
            ) : (
              <Navigate to="/login" replace />
            )
          } />

          {/* Role Selection - Special handling for role selection flow */}
          <Route path="/role-selection" element={
            showRoleSelection ? (
              <RoleSelectionScreen
                userName={user?.first_name || userName}
                onSelectRole={() => {}} // Will be handled by router
              />
            ) : (
              <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
            )
          } />

          <Route path="/account-success" element={<AccountSuccessScreen />} />

          {/* Protected Dashboard Routes - Only accessible when authenticated */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <PhotographerDashboard />
            </ProtectedRoute>
          } />

          {/* Test route to bypass protection temporarily */}
          <Route path="/test-dashboard" element={<PhotographerDashboard />} />
          <Route path="/gallery" element={
            <ProtectedRoute>
              <PhotoGallery />
            </ProtectedRoute>
          } />

          {/* Other Protected Pages */}
          <Route path="/events" element={
            <ProtectedRoute>
              <Events />
            </ProtectedRoute>
          } />
          <Route path="/event-detail-in-progress/:eventId" element={
            <ProtectedRoute>
              <EventDetailInProgress />
            </ProtectedRoute>
          } />
          <Route path="/event-detail-completed/:eventId" element={
            <ProtectedRoute>
              <EventDetailCompleted />
            </ProtectedRoute>
          } />
          <Route path="/event-detail-scheduled/:eventId" element={
            <ProtectedRoute>
              <EventDetailScheduled />
            </ProtectedRoute>
          } />
          {/* Legacy routes without event ID for backward compatibility */}
          <Route path="/event-detail-in-progress" element={
            <ProtectedRoute>
              <EventDetailInProgress />
            </ProtectedRoute>
          } />
          <Route path="/event-detail-completed" element={
            <ProtectedRoute>
              <EventDetailCompleted />
            </ProtectedRoute>
          } />
          <Route path="/event-detail-scheduled" element={
            <ProtectedRoute>
              <EventDetailScheduled />
            </ProtectedRoute>
          } />
          <Route path="/earnings" element={
            <ProtectedRoute>
              <div>Earnings Page - Coming Soon</div>
            </ProtectedRoute>
          } />
          <Route path="/product-stock" element={
            <ProtectedRoute>
              <div>Product Stock Page - Coming Soon</div>
            </ProtectedRoute>
          } />
          <Route path="/portfolio" element={
            <ProtectedRoute>
              <div>Portfolio Page - Coming Soon</div>
            </ProtectedRoute>
          } />
          <Route path="/calendar" element={
            <ProtectedRoute>
              <div>Calendar Page - Coming Soon</div>
            </ProtectedRoute>
          } />
          <Route path="/clients" element={
            <ProtectedRoute>
              <div>Clients Page - Coming Soon</div>
            </ProtectedRoute>
          } />
          <Route path="/invoice" element={
            <ProtectedRoute>
              <div>Invoice Page - Coming Soon</div>
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <div>Settings Page - Coming Soon</div>
            </ProtectedRoute>
          } />

          {/* Smart default redirect based on authentication status */}
          <Route path="/" element={<RootRedirect />} />
          <Route path="*" element={<RootRedirect />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;