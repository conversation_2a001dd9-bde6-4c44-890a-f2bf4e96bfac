import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { fetchDashboardData } from '../thunks/authThunks'
import { DashboardData, DashboardStats, QuickUpload, Notification, RecentEvent } from '../../services/authService'

export interface DashboardState {
  stats: DashboardStats | null
  quickUpload: QuickUpload | null
  notifications: Notification[]
  recentEvents: RecentEvent[]
  nearbyEvents: any[]
  isLoading: boolean
  error: string | null
  lastUpdated: string | null
}

const initialState: DashboardState = {
  stats: null,
  quickUpload: null,
  notifications: [],
  recentEvents: [],
  nearbyEvents: [],
  isLoading: false,
  error: null,
  lastUpdated: null,
}

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    clearDashboard: (state) => {
      return initialState
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDashboardData.pending, (state) => {
        console.log('Dashboard slice - fetchDashboardData pending')
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchDashboardData.fulfilled, (state, action: PayloadAction<DashboardData>) => {
        console.log('Dashboard slice - fetchDashboardData fulfilled with data:', action.payload)
        const { dashboard, generated_at } = action.payload

        state.stats = dashboard.stats
        state.quickUpload = dashboard.quick_upload
        state.notifications = dashboard.notifications
        state.recentEvents = dashboard.recent_events
        state.nearbyEvents = dashboard.nearby_events
        state.isLoading = false
        state.error = null
        state.lastUpdated = generated_at

        console.log('Dashboard slice - Updated state:', {
          stats: state.stats,
          notifications: state.notifications?.length,
          recentEvents: state.recentEvents?.length
        })
      })
      .addCase(fetchDashboardData.rejected, (state, action) => {
        console.log('Dashboard slice - fetchDashboardData rejected with error:', action.payload)
        state.isLoading = false
        state.error = action.payload as string
      })
      // Clear dashboard data on logout
      .addCase('auth/logout', (state) => {
        return initialState
      })
  },
})

export const { clearError, clearDashboard } = dashboardSlice.actions

// Selectors - Using any type to avoid circular imports
export const selectDashboard = (state: any) => state.dashboard
export const selectDashboardStats = (state: any) => state.dashboard.stats
export const selectDashboardNotifications = (state: any) => state.dashboard.notifications
export const selectDashboardRecentEvents = (state: any) => state.dashboard.recentEvents
export const selectDashboardQuickUpload = (state: any) => state.dashboard.quickUpload
export const selectDashboardLoading = (state: any) => state.dashboard.isLoading
export const selectDashboardError = (state: any) => state.dashboard.error

export default dashboardSlice.reducer
