// Token validation utilities

export interface DecodedToken {
  exp: number;
  iat: number;
  user_id: string;
  [key: string]: any;
}

/**
 * Decode JWT token without verification (client-side only)
 * This is safe for checking expiration but should not be used for security validation
 */
export const decodeToken = (token: string): DecodedToken | null => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = parts[1];
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));
    return decoded;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

/**
 * Check if a JWT token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  const decoded = decodeToken(token);
  if (!decoded || !decoded.exp) {
    return true; // Consider invalid tokens as expired
  }

  const currentTime = Math.floor(Date.now() / 1000);
  return decoded.exp < currentTime;
};

/**
 * Check if a JWT token will expire within the specified minutes
 */
export const isTokenExpiringSoon = (token: string, minutesThreshold: number = 5): boolean => {
  const decoded = decodeToken(token);
  if (!decoded || !decoded.exp) {
    return true;
  }

  const currentTime = Math.floor(Date.now() / 1000);
  const thresholdTime = currentTime + (minutesThreshold * 60);
  return decoded.exp < thresholdTime;
};

/**
 * Get token expiration time as Date object
 */
export const getTokenExpirationDate = (token: string): Date | null => {
  const decoded = decodeToken(token);
  if (!decoded || !decoded.exp) {
    return null;
  }

  return new Date(decoded.exp * 1000);
};

/**
 * Validate if token exists and is not expired
 */
export const isValidToken = (token: string | null): boolean => {
  if (!token) {
    return false;
  }

  return !isTokenExpired(token);
};
