import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Calendar,
  DollarSign,
  Search,
  ChevronDown,
  Camera,
  MapPin,
  Heart
} from 'lucide-react';
import Layout from '../../components/common/Layout';
import { Photo, Event } from '../../types/events';
import { AppDispatch } from '../../store/store';
import { selectAuth } from '../../store/slices/authSlice';
import {
  selectSelectedEvent,
  selectSelectedEventPhotos,
  selectEventsLoading,
  selectEventsError,
} from '../../store/slices/eventsSlice';
import {
  fetchEventDetails,
  fetchEventPhotos,
} from '../../store/thunks/eventsThunks';

interface EventDetailCompletedProps {
  eventId?: string;
}

const EventDetailCompleted: React.FC<EventDetailCompletedProps> = ({ eventId: propEventId }) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { eventId: urlEventId } = useParams<{ eventId: string }>();
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [eventData, setEventData] = useState<Event | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redux state
  const { tokens } = useSelector(selectAuth);
  const selectedEvent = useSelector(selectSelectedEvent);
  const eventPhotos = useSelector(selectSelectedEventPhotos);
  const loading = useSelector(selectEventsLoading);
  const reduxError = useSelector(selectEventsError);

  // Use URL eventId, then prop eventId, then selected event ID
  const currentEventId = urlEventId || propEventId || selectedEvent?.id;



  // Fetch event details and photos on mount
  useEffect(() => {
    const fetchEventData = async () => {
      if (!currentEventId || !tokens?.access) {
        console.log('🔍 EventDetailCompleted - Missing eventId or token:', {
          currentEventId,
          hasToken: !!tokens?.access
        });
        setIsLoading(false);
        return;
      }

      console.log('🔍 EventDetailCompleted - Fetching event data:', {
        eventId: currentEventId,
        urlEventId,
        propEventId,
        hasToken: !!tokens?.access
      });

      try {
        setIsLoading(true);
        setError(null);

        // Fetch event details using Redux thunk
        const eventResult = await dispatch(fetchEventDetails({
          eventId: currentEventId,
          token: tokens.access
        })).unwrap();

        console.log('🔍 EventDetailCompleted - Event data received:', eventResult);

        // Check if this event should be on this screen
        if (eventResult.event_status !== 'completed') {
          console.log('⚠️ Event status mismatch - redirecting:', {
            eventStatus: eventResult.event_status,
            expectedStatus: 'completed'
          });

          // Redirect to appropriate screen based on event status
          if (eventResult.event_status === 'ongoing' || eventResult.event_status === 'ongoing_with_buffer') {
            navigate(`/event-detail-in-progress/${currentEventId}`, { replace: true });
          } else if (eventResult.event_status === 'scheduled') {
            navigate(`/event-detail-scheduled/${currentEventId}`, { replace: true });
          }
          return;
        }

        setEventData(eventResult);

        // Fetch event photos
        dispatch(fetchEventPhotos({ eventId: currentEventId, token: tokens.access }));

      } catch (err: any) {
        console.error('💥 EventDetailCompleted - Error fetching event:', err);
        setError(err.message || 'Failed to load event details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchEventData();
  }, [dispatch, currentEventId, tokens?.access, urlEventId, propEventId, navigate]);

  // Use API event data, fallback to selected event from Redux
  const displayEvent = eventData || selectedEvent;

  // Convert API photos to UI format or use mock data
  const photos: Photo[] = eventPhotos?.photos?.map((photo: any) => ({
    id: photo.id,
    filename: photo.image_url.split('/').pop() || 'Unknown',
    size: 'Unknown', // Size not provided by API
    eventName: displayEvent?.name,
    uploadedOn: new Date(photo.uploaded_at).toLocaleDateString(),
    resolution: 'Unknown', // Resolution not provided by API
    downloads: 0, // Downloads not provided by API
    tags: [],
    thumbnail: photo.thumbnail_url,
    image_url: photo.image_url,
    photographer: photo.photographer
  })) || [
    // Mock photos for testing
    { id: '1', filename: 'IMG_21165.HEIC', size: '3.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '2', filename: 'IMG_21166.HEIC', size: '5.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '3', filename: 'IMG_21167.HEIC', size: '3.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '4', filename: 'IMG_21168.HEIC', size: '1.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '5', filename: 'IMG_21169.HEIC', size: '3.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '6', filename: 'IMG_21170.HEIC', size: '5.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '7', filename: 'IMG_21171.HEIC', size: '3.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '8', filename: 'IMG_21172.HEIC', size: '1.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '9', filename: 'IMG_21173.HEIC', size: '3.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '10', filename: 'IMG_21174.HEIC', size: '5.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '11', filename: 'IMG_21175.HEIC', size: '3.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null },
    { id: '12', filename: 'IMG_21176.HEIC', size: '1.4 MB', eventName: displayEvent.name, uploadedOn: '5 days ago', resolution: '4021 x 3024', downloads: 0, tags: [], thumbnail: '', image_url: '', photographer: null }
  ];





  const handlePhotoSelect = (photo: Photo) => {
    setSelectedPhoto(photo);
  };

  // Debug logging
  console.log('🔍 EventDetailCompleted - Render state:', {
    displayEvent,
    isLoading,
    error,
    currentEventId,
    eventData,
    selectedEvent
  });

  return (
    <Layout
      searchPlaceholder="Search here"
      showRightSidebar={true}
      selectedPhoto={selectedPhoto}
      onPhotoClose={() => setSelectedPhoto(null)}
    >
      {/* Left Side - Event Details */}
      <div className="flex-1 p-6 overflow-y-auto">
        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <div className="text-gray-500">Loading event details...</div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex justify-center items-center py-12">
            <div className="text-red-500">Error loading event: {error}</div>
          </div>
        )}

        {/* Event Header Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-blue-600 font-bold text-2xl">
                {displayEvent.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900 text-left">{displayEvent.name}</h1>
                <span className="px-3 py-1 text-sm font-medium text-green-600 bg-green-100 rounded-full">
                  Completed
                </span>
              </div>
              <p className="text-gray-600 text-left">{displayEvent.description || 'No description available'}</p>
            </div>
          </div>

          {/* Overview Tab */}
          <div className="flex gap-8 border-b border-gray-200">
            <button className="text-sm font-medium text-blue-600 border-b-2 border-blue-600 pb-2">
              Overview
            </button>
            <button className="text-sm font-medium text-gray-500 hover:text-gray-700 pb-2">
              Revenue
            </button>
          </div>
        </div>

        {/* Event Details Cards */}
        <div className="grid grid-cols-2 gap-6 mb-6">
          {/* Date Card */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Calendar size={20} className="text-blue-600" />
              </div>
            </div>
            <div className="text-left">
              <p className="text-sm text-gray-500 mb-1">Date</p>
              <p className="text-2xl font-bold text-gray-900 mb-1">Dec 12, 2022</p>
              <p className="text-xs text-gray-500">(Completed on Nov 07, 2023)</p>
            </div>
          </div>

          {/* Revenue Mode Card */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign size={20} className="text-green-600" />
              </div>
            </div>
            <div className="text-left">
              <p className="text-sm text-gray-500 mb-1">Revenue Made</p>
              <p className="text-2xl font-bold text-gray-900 mb-1">$3,632.30</p>
              <p className="text-xs text-gray-500">Pay Per Photo</p>
            </div>
          </div>

          {/* Photos Taken Card */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Camera size={20} className="text-purple-600" />
              </div>
            </div>
            <div className="text-left">
              <p className="text-sm text-gray-500 mb-1">Photos Taken</p>
              <p className="text-2xl font-bold text-gray-900">250</p>
            </div>
          </div>

          {/* Location Card */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <MapPin size={20} className="text-orange-600" />
              </div>
            </div>
            <div className="text-left">
              <p className="text-sm text-gray-500 mb-1">Location</p>
              <p className="text-lg font-bold text-gray-900">Marina Beach, Chennai, Tamil Nadu</p>
            </div>
          </div>
        </div>





        {/* Uploaded Images Section */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">Uploaded Images</h2>
            <div className="flex items-center gap-6">
              <div className="flex gap-8">
                <button className="text-sm font-medium text-blue-600 border-b-2 border-blue-600 pb-1">
                  All Uploaded Images
                </button>
                <button className="text-sm font-medium text-gray-500 hover:text-gray-700">
                  Filtered Images
                </button>
                <button className="text-sm font-medium text-gray-500 hover:text-gray-700">
                  Videos
                </button>
              </div>
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search project"
                    className="pl-9 pr-4 py-2 w-48 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
                <div className="relative">
                  <select className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="newest">Newest</option>
                    <option value="oldest">Oldest</option>
                  </select>
                  <ChevronDown size={16} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>
          </div>

          {/* Photo Grid */}
          <div className="grid grid-cols-4 gap-6">
            {photos.map((photo) => (
              <div
                key={photo.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden cursor-pointer group hover:shadow-md transition-shadow"
                onClick={() => handlePhotoSelect(photo)}
              >
                <div className="relative">
                  <div className="w-full h-40 bg-gray-300">
                    {/* Placeholder for image */}
                  </div>
                  <button className="absolute top-2 right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-sm opacity-0 group-hover:opacity-100 transition-opacity">
                    <Heart size={14} className="text-gray-400" />
                  </button>
                </div>
                <div className="p-3">
                  <p className="text-sm font-medium text-gray-900 text-left">{photo.filename}</p>
                  <p className="text-xs text-gray-500 text-left">{photo.size}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default EventDetailCompleted;
