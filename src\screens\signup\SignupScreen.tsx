import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Eye, EyeOff } from 'lucide-react';
import { AppDispatch } from '../../store/store';
import { signupUser } from '../../store/thunks/authThunks';
import { clearError, selectAuth } from '../../store/slices/authSlice';
import photofishLogo from '../../assets/images/photofish-logo.svg';

const SignupScreen: React.FC = () => {
  const navigate = useNavigate();
  // Local form state
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  // Local validation states
  const [firstNameError, setFirstNameError] = useState('');
  const [lastNameError, setLastNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [termsError, setTermsError] = useState('');

  // Redux integration
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error, pendingUserId } = useSelector(selectAuth);

  // Clear Redux errors when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Handle successful signup - navigate to OTP
  useEffect(() => {
    if (pendingUserId && !isLoading && !error) {
      navigate('/otp');
    }
  }, [pendingUserId, isLoading, error, navigate]);

  // Email validation function
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Handle email change with validation
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);

    if (newEmail && !validateEmail(newEmail)) {
      setEmailError('Please enter a valid email address');
    } else {
      setEmailError('');
    }
  };

  // Handle confirm password change with validation
  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newConfirmPassword = e.target.value;
    setConfirmPassword(newConfirmPassword);

    if (password && newConfirmPassword && password !== newConfirmPassword) {
      setPasswordError('Passwords do not match');
    } else {
      setPasswordError('');
    }
  };

  // Handle password change and revalidate confirm password
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);

    if (confirmPassword && newPassword && newPassword !== confirmPassword) {
      setPasswordError('Passwords do not match');
    } else {
      setPasswordError('');
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setFirstNameError('');
    setLastNameError('');
    setEmailError('');
    setPasswordError('');
    setTermsError('');
    dispatch(clearError());

    let hasErrors = false;

    // Validate first name
    if (!firstName.trim()) {
      setFirstNameError('First name is required');
      hasErrors = true;
    }

    // Validate last name
    if (!lastName.trim()) {
      setLastNameError('Last name is required');
      hasErrors = true;
    }

    // Validate email
    if (!email) {
      setEmailError('Email is required');
      hasErrors = true;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      hasErrors = true;
    }

    // Validate passwords
    if (!password) {
      setPasswordError('Password is required');
      hasErrors = true;
    } else if (!confirmPassword) {
      setPasswordError('Please confirm your password');
      hasErrors = true;
    } else if (password !== confirmPassword) {
      setPasswordError('Passwords do not match');
      hasErrors = true;
    }

    // Validate terms agreement
    if (!agreeToTerms) {
      setTermsError('You must agree to the Terms & Conditions');
      hasErrors = true;
    }

    // If all validations pass, make API call
    if (!hasErrors) {
      try {
        await dispatch(signupUser({
          first_name: firstName.trim(),
          last_name: lastName.trim(),
          email: email.trim(),
          password: password.trim(),
          confirm_password: confirmPassword.trim()
        })).unwrap();
        
        // Success - navigation handled by useEffect above
      } catch (err) {
        // Error handled by Redux state
        console.error('Signup failed:', err);
      }
    }
  };

  return (
    <div className="min-h-screen flex bg-gray-100">
      {/* Left Side - Sign Up Form */}
      <div className="w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-sm">
          {/* Logo */}
          <div className="mb-8">
            <div className="flex items-center">
              <img
                src={photofishLogo}
                alt="PhotoFish Logo"
                className="h-8 w-auto"
              />
            </div>
          </div>

          {/* Sign Up Header */}
          <h1 className="text-2xl font-bold text-gray-900 mb-6 text-left">Sign up</h1>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-300 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 text-left">
                  First Name
                </label>
                <input
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  disabled={isLoading}
                  className={`w-full px-3 py-2.5 border rounded-lg focus:ring-2 focus:ring-black focus:border-transparent outline-none text-sm ${
                    firstNameError ? 'border-red-500' : 'border-gray-300'
                  } ${isLoading ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  placeholder="John"
                />
                {firstNameError && (
                  <p className="text-red-500 text-sm mt-1 text-left">{firstNameError}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 text-left">
                  Last Name
                </label>
                <input
                  type="text"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  disabled={isLoading}
                  className={`w-full px-3 py-2.5 border rounded-lg focus:ring-2 focus:ring-black focus:border-transparent outline-none text-sm ${
                    lastNameError ? 'border-red-500' : 'border-gray-300'
                  } ${isLoading ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  placeholder="Doe"
                />
                {lastNameError && (
                  <p className="text-red-500 text-sm mt-1 text-left">{lastNameError}</p>
                )}
              </div>
            </div>

            {/* Email Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 text-left">
                Email Address
              </label>
              <input
                type="email"
                value={email}
                onChange={handleEmailChange}
                disabled={isLoading}
                className={`w-full px-3 py-2.5 border rounded-lg focus:ring-2 focus:ring-black focus:border-transparent outline-none text-sm ${
                  emailError ? 'border-red-500' : 'border-gray-300'
                } ${isLoading ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                placeholder="<EMAIL>"
              />
              {emailError && (
                <p className="text-red-500 text-sm mt-1 text-left">{emailError}</p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 text-left">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={handlePasswordChange}
                  disabled={isLoading}
                  className={`w-full px-3 py-2.5 border rounded-lg focus:ring-2 focus:ring-black focus:border-transparent outline-none pr-10 text-sm ${
                    passwordError ? 'border-red-500' : 'border-gray-300'
                  } ${isLoading ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
            </div>

            {/* Confirm Password Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 text-left">
                Confirm Password
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  disabled={isLoading}
                  className={`w-full px-3 py-2.5 border rounded-lg focus:ring-2 focus:ring-black focus:border-transparent outline-none pr-10 text-sm ${
                    passwordError ? 'border-red-500' : 'border-gray-300'
                  } ${isLoading ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isLoading}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
              {passwordError && (
                <p className="mt-1 text-sm text-red-600 text-left">{passwordError}</p>
              )}
            </div>

            {/* Sign Up Button */}
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-2.5 rounded-lg font-medium transition-colors text-sm ${
                isLoading 
                  ? 'bg-gray-400 cursor-not-allowed text-white' 
                  : 'bg-black text-white hover:bg-gray-800'
              }`}
            >
              {isLoading ? 'Creating account...' : 'Sign up'}
            </button>

            {/* Terms Agreement */}
            <div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="terms"
                  checked={agreeToTerms}
                  onChange={(e) => setAgreeToTerms(e.target.checked)}
                  disabled={isLoading}
                  className="w-4 h-4 text-black border-gray-300 rounded focus:ring-black"
                />
                <label htmlFor="terms" className="ml-2 text-sm text-gray-700 text-left">
                  I agree to the{' '}
                  <a href="#" className="text-black hover:underline">Terms & Conditions</a>
                </label>
              </div>
              {termsError && (
                <p className="text-red-500 text-sm mt-1 text-left">{termsError}</p>
              )}
            </div>
          </form>

          {/* Links */}
          <div className="mt-4 text-sm text-left">
            <p className="text-gray-600">
              Already have an account?{' '}
              <Link
                to="/login"
                className="text-black hover:underline"
              >
                Log in
              </Link>
            </p>
          </div>

          {/* Social Login */}
          <div className="mt-6">
            <button 
              disabled={isLoading}
              className={`w-full flex items-center justify-center py-2.5 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors ${
                isLoading ? 'cursor-not-allowed opacity-50' : ''
              }`}
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span className="text-sm font-medium text-gray-700">Continue with Google</span>
            </button>
          </div>
        </div>
      </div>

      {/* Right Side - Black Panel */}
      <div className="w-1/2 pt-12 pr-12 pb-12">
        <div className="bg-black rounded-3xl shadow-2xl h-full flex items-center justify-center">
          <div className="text-gray-400 text-center">
            <div className="w-24 h-24 bg-gray-800 rounded-2xl mx-auto mb-8 flex items-center justify-center">
              <svg className="w-12 h-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <p className="text-xl font-medium text-white">Image Placeholder</p>
            <p className="text-gray-400 mt-2">Your content goes here</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignupScreen;