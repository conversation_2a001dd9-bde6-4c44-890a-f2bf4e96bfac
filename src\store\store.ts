import { configureStore } from '@reduxjs/toolkit'
import { persistStore, persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import { combineReducers } from '@reduxjs/toolkit'
import authReducer from './slices/authSlice'
import dashboardReducer from './slices/dashboardSlice'
import eventsReducer from './slices/eventsSlice'

const persistConfig = {
  key: 'photofish',
  storage,
  whitelist: ['auth'], // Only persist auth state, not dashboard (for debugging)
}

const rootReducer = combineReducers({
  auth: authReducer,
  dashboard: dashboardReducer,
  events: eventsReducer,
})

const persistedReducer = persistReducer(persistConfig, rootReducer)

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
})

export const persistor = persistStore(store)
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// Selectors for easy access to state
export const selectAuth = (state: RootState) => state.auth
export const selectUser = (state: RootState) => state.auth.user
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated
export const selectIsLoading = (state: RootState) => state.auth.isLoading
export const selectError = (state: RootState) => state.auth.error

// Events selectors
export const selectEvents = (state: RootState) => state.events
export const selectAllEvents = (state: RootState) => state.events.allEvents
export const selectSelectedEvent = (state: RootState) => state.events.selectedEvent
export const selectEventsLoading = (state: RootState) => state.events.loading
export const selectEventsError = (state: RootState) => state.events.error