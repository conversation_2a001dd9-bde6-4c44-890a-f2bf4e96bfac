import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import photofishLogo from '../../assets/images/photofish-logo.svg';

const AccountSuccessScreen: React.FC = () => {
  const navigate = useNavigate();

  const handleContinue = () => {
    console.log('Continue button clicked');
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen flex bg-gray-100">
      {/* Left Side - Success Message */}
      <div className="w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-sm">
          {/* Logo */}
          <div className="mb-8">
            <div className="flex items-center">
              <img
                src={photofishLogo}
                alt="PhotoFish Logo"
                className="h-8 w-auto"
              />
            </div>
          </div>

          {/* Success Header */}
          <h1 className="text-2xl font-bold text-gray-900 mb-6 text-left">
            Your Account Has Been Created Successfully
          </h1>

          {/* Continue Button */}
          <div className="mb-6">
            <button
              onClick={handleContinue}
              className="w-full bg-black text-white py-2.5 rounded-lg font-medium hover:bg-gray-800 transition-colors text-sm"
            >
              Continue
            </button>
          </div>

          {/* Support Text */}
          <div className="text-sm text-left">
            <p className="text-gray-900 mb-1">If you need further assistance</p>
            <Link to="/login" className="text-gray-600 hover:underline">
              contact our support team
            </Link>
          </div>
        </div>
      </div>

      {/* Right Side - Black Panel (matching other screens) */}
      <div className="w-1/2 pt-12 pr-12 pb-12">
        <div className="bg-black rounded-3xl shadow-2xl h-full flex items-center justify-center">
          <div className="text-gray-400 text-center">
            <div className="text-6xl mb-4">✅</div>
            <p className="text-lg">Account Created</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountSuccessScreen;