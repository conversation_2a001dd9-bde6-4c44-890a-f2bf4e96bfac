import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Calendar,
  DollarSign,
  Plus,
  TrendingUp,
  Bell,
  Camera,
  CheckCircle,
  XCircle,
  Clock,
  UserPlus
} from 'lucide-react';
import Layout from '../../components/common/Layout';
import { fetchDashboardData } from '../../store/thunks/authThunks';
import {
  selectDashboardStats,
  selectDashboardNotifications,
  selectDashboardRecentEvents,
  selectDashboardQuickUpload,
  selectDashboardLoading,
  selectDashboardError
} from '../../store/slices/dashboardSlice';
import { selectAuth, logout } from '../../store/slices/authSlice';

const PhotographerDashboard: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const dashboardStats = useSelector(selectDashboardStats);
  const notifications = useSelector(selectDashboardNotifications);
  const recentEvents = useSelector(selectDashboardRecentEvents);
  const quickUpload = useSelector(selectDashboardQuickUpload);
  const nearbyEvents = useSelector((state: any) => state.dashboard.nearbyEvents);
  const isLoading = useSelector(selectDashboardLoading);
  const error = useSelector(selectDashboardError);
  const { isAuthenticated, tokens } = useSelector(selectAuth);

  // State for photographer requests
  const [photographerRequests, setPhotographerRequests] = useState<any[]>([]);
  const [requestsLoading, setRequestsLoading] = useState(false);

  // Ref to prevent multiple simultaneous API calls
  const fetchingRef = useRef(false);

  // Handle inconsistent authentication state
  useEffect(() => {
    if (isAuthenticated && !tokens?.access) {
      console.log('🚨 INCONSISTENT AUTH STATE: User is authenticated but has no token');
      console.log('🚨 This usually means the token was cleared but auth state wasn\'t updated');
      console.log('🚨 Clearing auth state and redirecting to login...');

      // Clear the authentication state and redirect to login
      dispatch(logout());
      navigate('/login', { replace: true });
      return;
    }
  }, [isAuthenticated, tokens, dispatch, navigate]);

  // Fetch dashboard data on component mount - FIXED: Prevent infinite loop
  useEffect(() => {
    console.log('Dashboard component mounted');
    console.log('Auth state:', {
      isAuthenticated,
      hasToken: !!tokens?.access,
      tokenValue: tokens?.access ? tokens.access.substring(0, 50) + '...' : 'null'
    });

    // Only fetch if authenticated, has token, don't have data yet, and not already fetching
    if (isAuthenticated && tokens?.access && !dashboardStats && !isLoading && !fetchingRef.current) {
      console.log('✅ Fetching dashboard data (first time)');
      fetchingRef.current = true;
      dispatch(fetchDashboardData() as any).finally(() => {
        fetchingRef.current = false;
      });
    }
  }, [dispatch, isAuthenticated, tokens?.access, dashboardStats, isLoading]);

  // Fetch photographer requests
  const fetchPhotographerRequests = async () => {
    if (!tokens?.access) return;

    setRequestsLoading(true);
    try {
      const response = await fetch('http://localhost:8000/api/v1/events/my-photographer-requests/', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tokens.access}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Photographer requests data:', data);
        console.log('Individual request statuses:', data.requests?.map((r: any) => ({ id: r.id, status: r.status, status_display: r.status_display })));
        setPhotographerRequests(data.requests || []);
      } else {
        console.error('Failed to fetch photographer requests:', response.status);
      }
    } catch (error) {
      console.error('Error fetching photographer requests:', error);
    } finally {
      setRequestsLoading(false);
    }
  };

  // Fetch photographer requests when component mounts
  useEffect(() => {
    if (isAuthenticated && tokens?.access) {
      fetchPhotographerRequests();
    }
  }, [isAuthenticated, tokens?.access]);

  // Removed excessive debug logging to prevent infinite loops

  // Map API data to component format
  const statsData = dashboardStats ? [
    {
      title: dashboardStats.total_images.label,
      value: dashboardStats.total_images.count.toLocaleString(),
      icon: Camera,
      color: "text-blue-600",
      bgColor: "bg-blue-100"
    },
    {
      title: dashboardStats.total_events.label,
      value: dashboardStats.total_events.count.toString(),
      icon: Calendar,
      color: "text-orange-600",
      bgColor: "bg-orange-100"
    },
    {
      title: dashboardStats.total_revenue.label,
      value: `$${dashboardStats.total_revenue.amount}`,
      subText: `$${dashboardStats.total_revenue.pending_disbursement} To be disbursed`,
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-100"
    }
  ] : [
    // Fallback data while loading
    {
      title: "TOTAL IMAGES TAKEN",
      value: "0",
      icon: Camera,
      color: "text-blue-600",
      bgColor: "bg-blue-100"
    },
    {
      title: "TOTAL EVENTS ATTENDED",
      value: "0",
      icon: Calendar,
      color: "text-orange-600",
      bgColor: "bg-orange-100"
    },
    {
      title: "TOTAL REVENUE",
      value: "$0.00",
      subText: "$0.00 To be disbursed",
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-100"
    }
  ];

  // Map API recent events data to component format
  const recentEventsData = recentEvents?.map((event: any) => ({
    eventName: event.name,
    eventDate: event.date,
    status: event.status.toLowerCase()
  })) || [];

  // Map API notifications data to component format
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'payment_received':
      case 'payment_disbursed':
        return { icon: DollarSign, bgColor: "bg-green-100", iconColor: "text-green-600" };
      case 'payment_pending':
        return { icon: DollarSign, bgColor: "bg-orange-100", iconColor: "text-orange-600" };
      case 'event_accepted':
        return { icon: Calendar, bgColor: "bg-blue-100", iconColor: "text-blue-600" };
      case 'event_invitation':
        return { icon: Bell, bgColor: "bg-purple-100", iconColor: "text-purple-600" };
      default:
        return { icon: Bell, bgColor: "bg-gray-100", iconColor: "text-gray-600" };
    }
  };

  const notificationsData = notifications?.map((notification: any) => {
    const iconConfig = getNotificationIcon(notification.type);
    return {
      title: notification.title,
      message: notification.message,
      time: notification.time_ago,
      type: notification.type,
      icon: iconConfig.icon,
      bgColor: iconConfig.bgColor,
      iconColor: iconConfig.iconColor
    };
  }) || [];

  // Handle request actions
  const handleWithdrawRequest = async (requestId: string) => {
    console.log('Withdrawing request:', requestId);
    // TODO: Implement withdraw request API call
  };

  const handleJoinEvent = async (eventId: string) => {
    console.log('Joining event:', eventId);
    // TODO: Implement join event API call
  };

  // Map photographer requests data to component format
  const getRequestIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return { icon: CheckCircle, bgColor: "bg-green-50", iconColor: "text-green-600", iconBgColor: "bg-green-100" };
      case 'DENIED':
        return { icon: XCircle, bgColor: "bg-red-50", iconColor: "text-red-600", iconBgColor: "bg-red-100" };
      case 'PENDING':
        return { icon: Clock, bgColor: "bg-yellow-50", iconColor: "text-yellow-600", iconBgColor: "bg-yellow-100" };
      case 'INVITED':
        return { icon: UserPlus, bgColor: "bg-blue-50", iconColor: "text-blue-600", iconBgColor: "bg-blue-100" };
      default:
        return { icon: Bell, bgColor: "bg-gray-50", iconColor: "text-gray-600", iconBgColor: "bg-gray-100" };
    }
  };

  const requestsData = photographerRequests?.map((request: any) => {
    console.log('Processing request:', request.status, request.status_display);
    // Handle both status field and status_display field for pending requests
    const statusToUse = request.status_display === 'Pending Approval' ? 'PENDING' : request.status;
    const iconConfig = getRequestIcon(statusToUse);
    const eventDate = new Date(request.event_date).toLocaleDateString();
    const createdDate = new Date(request.created_at);
    const timeAgo = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60)) + ' hours ago';

    let actionButton = null;
    if (request.status === 'PENDING' || request.status_display === 'Pending Approval') {
      actionButton = {
        text: 'Withdraw Request',
        className: 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200',
        onClick: handleWithdrawRequest
      };
    } else if (request.status === 'INVITED') {
      actionButton = {
        text: 'Join Event',
        className: 'bg-blue-100 text-blue-700 hover:bg-blue-200',
        onClick: () => handleJoinEvent(request.event_id)
      };
    }

    return {
      id: request.id,
      title: request.status_display,
      message: `Your request to photograph "${request.event_name}" has been ${request.status.toLowerCase()}`,
      time: timeAgo,
      status: request.status,
      icon: iconConfig.icon,
      bgColor: iconConfig.bgColor,
      iconColor: iconConfig.iconColor,
      iconBgColor: iconConfig.iconBgColor,
      actionButton
    };
  }) || [];

  // Show loading state
  if (isLoading) {
    return (
      <Layout searchPlaceholder="Search here...">
        <div className="bg-gray-50 px-6 py-4">
          <h1 className="text-2xl font-semibold text-gray-900 text-left">Photographer Dashboard</h1>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Show error state
  if (error) {
    return (
      <Layout searchPlaceholder="Search here...">
        <div className="bg-gray-50 px-6 py-4">
          <h1 className="text-2xl font-semibold text-gray-900 text-left">Photographer Dashboard</h1>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-12 h-12 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Bell size={20} className="text-red-600" />
            </div>
            <p className="text-red-600 mb-2">Failed to load dashboard data</p>
            <p className="text-gray-500 text-sm mb-4">{error}</p>
            <button
              onClick={() => dispatch(fetchDashboardData() as any)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout searchPlaceholder="Search here...">
      {/* Dashboard Title */}
      <div className="bg-gray-50 px-6 py-4 flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 text-left">Photographer Dashboard</h1>

        {/* Debug buttons - Remove in production */}
        <div className="flex gap-2">
          <button
            onClick={() => {
              console.log('🔧 Current auth state:', { isAuthenticated, tokens });
              console.log('🔧 Dashboard state:', { isLoading, error, dashboardStats });
            }}
            className="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm hover:bg-blue-200"
          >
            Debug State
          </button>
          <button
            onClick={() => {
              console.log('🧹 Clearing localStorage and auth state...');
              localStorage.clear();
              dispatch(logout());
              navigate('/login', { replace: true });
            }}
            className="px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200"
          >
            Clear & Logout
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6">
          {/* Stats Cards - Horizontal Row at Top */}
          <div className="grid grid-cols-3 gap-6">
            {statsData.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 relative">
                {/* Icon in top-right corner */}
                <div className={`absolute top-4 right-4 w-10 h-10 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                  <stat.icon size={20} className={stat.color} />
                </div>
                
                <div className="pr-16">
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2 text-left">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 mb-1 text-left">
                    {stat.value}
                  </p>
                  {stat.subText && (
                    <p className="text-sm text-red-500 mt-1 text-left">
                      {stat.subText}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Two Column Layout Below Stats */}
          <div className="grid grid-cols-12 gap-6">
            {/* Left Column - Takes up 4 columns */}
            <div className="col-span-4 space-y-6">
              {/* Quick Add Photos Card */}
              <div className="bg-blue-600 rounded-xl p-6 text-white">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                    <Plus size={24} />
                  </div>
                  <div>
                    {(() => {
                      // Check for active events first, then nearby available events
                      const hasActiveEvents = quickUpload?.has_active_events;
                      const hasAvailableEvents = nearbyEvents && nearbyEvents.length > 0;
                      const primaryEvent = quickUpload?.primary_event;
                      const availableEvent = nearbyEvents?.[0]; // First available event

                      if (hasActiveEvents && primaryEvent) {
                        return (
                          <>
                            <h3 className="text-lg font-semibold text-left">{primaryEvent.name}</h3>
                            <p className="text-blue-100 text-sm text-left">QUICK ADD PHOTOS</p>
                          </>
                        );
                      } else if (hasAvailableEvents && availableEvent) {
                        return (
                          <>
                            <h3 className="text-lg font-semibold text-left">{availableEvent.name}</h3>
                            <p className="text-blue-100 text-sm text-left">AVAILABLE EVENT - QUICK ADD PHOTOS</p>
                          </>
                        );
                      } else {
                        return (
                          <>
                            <h3 className="text-lg font-semibold text-left">No Active Event</h3>
                            <p className="text-blue-100 text-sm text-left">NO ACTIVE EVENTS</p>
                          </>
                        );
                      }
                    })()}
                  </div>
                </div>
              </div>

              {/* Recent Events */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 text-left">Recent Events</h3>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex justify-between text-xs text-gray-500 font-medium">
                      <span>Event Name</span>
                      <span>Event Date</span>
                    </div>
                    {recentEventsData.length > 0 ? (
                      recentEventsData.map((event: any, index: number) => (
                        <div key={index} className="flex items-center justify-between py-2">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <Calendar size={14} className="text-blue-600" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-900 text-left">{event.eventName}</p>
                              <p className="text-xs text-gray-500 text-left capitalize">{event.status}</p>
                            </div>
                          </div>
                          <p className="text-sm text-gray-700 text-right">{event.eventDate}</p>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                          <Calendar size={20} className="text-gray-400" />
                        </div>
                        <p className="text-sm text-gray-500">Nothing to see here</p>
                        <p className="text-xs text-gray-400 mt-1">No recent events found</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Takes up 8 columns */}
            <div className="col-span-8">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-fit">
                <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 text-left">Request Activity</h3>
                  <button className="text-sm text-blue-600 hover:text-blue-700 font-medium text-right">
                    Mark all as read
                  </button>
                </div>
                {/* Scrollable requests container */}
                <div className="max-h-96 overflow-y-auto">
                  <div className="p-6">
                    <div className="space-y-4">
                      {requestsData.length > 0 ? (
                        requestsData.map((request: any, index: number) => (
                          <div key={index} className={`flex items-start gap-4 p-4 rounded-lg hover:bg-gray-100 transition-colors ${request.bgColor}`}>
                            <div className={`w-10 h-10 ${request.iconBgColor} rounded-lg flex items-center justify-center flex-shrink-0`}>
                              <request.icon size={16} className={request.iconColor} />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center gap-2">
                                  <p className="text-sm font-medium text-gray-900 text-left">{request.title}</p>
                                  <span className="text-xs text-gray-500">{request.time}</span>
                                </div>
                                {request.actionButton && (
                                  <button
                                    className={`px-3 py-1 text-xs font-medium rounded-full ${request.actionButton.className}`}
                                    onClick={() => request.actionButton.onClick(request.id)}
                                  >
                                    {request.actionButton.text}
                                  </button>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 text-left">{request.message}</p>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                            <Bell size={20} className="text-gray-400" />
                          </div>
                          <p className="text-sm text-gray-500">No requests</p>
                          <p className="text-xs text-gray-400 mt-1">You're all caught up!</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Events Near By Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 text-left">Events Near By</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-white">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Event Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Paying Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date - Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 text-left">Wedding Photography</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 text-left">Per Hour</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 text-left">Dec 15, 2024 - 2:00 PM</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 text-left">Downtown Hotel</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        Available
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900">Apply</button>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 text-left">Corporate Event</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 text-left">Fixed Rate</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 text-left">Dec 18, 2024 - 10:00 AM</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 text-left">Business Center</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        Pending
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-left text-sm font-medium">
                      <button className="text-gray-400 cursor-not-allowed">Applied</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default PhotographerDashboard;
