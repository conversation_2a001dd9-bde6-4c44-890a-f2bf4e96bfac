import React from 'react';
import {
  Heart,
  Download,
  Trash2
} from 'lucide-react';
import { Photo } from '../../types/events';

interface RightSidebarProps {
  selectedPhoto: Photo | null;
  onClose?: () => void;
  showSharedWith?: boolean;
}

const RightSidebar: React.FC<RightSidebarProps> = ({ 
  selectedPhoto, 
  onClose,
  showSharedWith = false 
}) => {
  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
      {selectedPhoto ? (
        <>
          {/* Selected Image Preview */}
          <div className="p-6 border-b border-gray-200">
            <div className="w-full h-64 bg-gray-300 rounded-lg mb-4 flex items-center justify-center">
              {/* Visible placeholder box for selected image */}
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-900 text-left">{selectedPhoto.filename}</p>
                <p className="text-xs text-gray-500 text-left">{selectedPhoto.size}</p>
              </div>
              <button className="p-2 hover:bg-gray-100 rounded-lg">
                <Heart size={16} className="text-gray-400" />
              </button>
            </div>
          </div>

          {/* Information Section */}
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-sm font-semibold text-gray-900 mb-4 text-left">Information</h3>
            <div className="space-y-3">
              {selectedPhoto.eventName && (
                <div className="flex justify-between">
                  <span className="text-xs text-gray-500">Event name</span>
                  <span className="text-xs text-gray-900 font-medium">{selectedPhoto.eventName}</span>
                </div>
              )}
              {selectedPhoto.uploadedOn && (
                <div className="flex justify-between">
                  <span className="text-xs text-gray-500">Uploaded on</span>
                  <span className="text-xs text-gray-900 font-medium">{selectedPhoto.uploadedOn}</span>
                </div>
              )}
              {selectedPhoto.resolution && (
                <div className="flex justify-between">
                  <span className="text-xs text-gray-500">Resolution</span>
                  <span className="text-xs text-gray-900 font-medium">{selectedPhoto.resolution}</span>
                </div>
              )}
              {selectedPhoto.downloads !== undefined && (
                <div className="flex justify-between">
                  <span className="text-xs text-gray-500">Downloads</span>
                  <span className="text-xs text-gray-900 font-medium">{selectedPhoto.downloads}</span>
                </div>
              )}
            </div>
          </div>

          {/* Event Description Section */}
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-sm font-semibold text-gray-900 mb-4 text-left">Event Description</h3>
            <p className="text-xs text-gray-500 text-left">
              Add an event description to this image
            </p>
          </div>

          {/* Photo Tags Section */}
          <div className="p-6 border-b border-gray-200 flex-1">
            <h3 className="text-sm font-semibold text-gray-900 mb-4 text-left">Photo Tags</h3>
            <p className="text-xs text-gray-500 text-left">
              {selectedPhoto.tags ? selectedPhoto.tags.join(', ') : 'conference, tech, presentation, networking'}
            </p>
          </div>

          {/* Shared With Section - Optional */}
          {showSharedWith && (
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-sm font-semibold text-gray-900 mb-4 text-left">Shared with</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                    <span className="text-xs text-gray-900">Dave Xander</span>
                  </div>
                  <button className="text-xs text-blue-600 hover:text-blue-700">Remove</button>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                    <span className="text-xs text-gray-900">Maxima Smalls</span>
                  </div>
                  <button className="text-xs text-blue-600 hover:text-blue-700">Remove</button>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="p-6 space-y-3">
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center justify-center gap-2">
              <Download size={16} />
              Download
            </button>
            <button className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center justify-center gap-2">
              <Trash2 size={16} />
              Delete image
            </button>
          </div>
        </>
      ) : (
        /* No Image Selected State */
        <div className="flex-1 flex items-center justify-center p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
              <Heart size={24} className="text-gray-400" />
            </div>
            <p className="text-sm text-gray-500">Select an image to view details</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default RightSidebar;
