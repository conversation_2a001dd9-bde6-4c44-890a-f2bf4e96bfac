import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { selectRole, selectAuth } from '../../store/slices/authSlice';
import { AppDispatch } from '../../store/store';
import photofishLogo from '../../assets/images/photofish-logo.svg';

interface RoleSelectionScreenProps {
  userName?: string;
  onSelectRole?: (role: 'admin' | 'user' | 'photographer') => void;
}

const RoleSelectionScreen: React.FC<RoleSelectionScreenProps> = ({
  userName = 'Jack',
  onSelectRole
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { user } = useSelector(selectAuth);

  const handleRoleSelection = (role: 'admin' | 'user' | 'photographer') => {
    console.log(`Selected role: ${role}`);

    // Dispatch role selection to Redux
    const mappedRole = role === 'admin' ? 'ADMIN' : role === 'photographer' ? 'PHOTOGRAPHER' : 'USER';
    dispatch(selectRole(mappedRole as 'USER' | 'PHOTOGRAPHER' | 'ADMIN'));

    // Navigate to dashboard
    navigate('/dashboard');

    // Call the prop callback if provided
    if (onSelectRole) {
      onSelectRole(role);
    }
  };

  return (
    <div className="min-h-screen flex bg-gray-100">
      {/* Left Side - Role Selection */}
      <div className="w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-sm">
          {/* Logo */}
          <div className="mb-8">
            <div className="flex items-center">
              <img
                src={photofishLogo}
                alt="PhotoFish Logo"
                className="h-8 w-auto"
              />
            </div>
          </div>

          {/* Greeting Header */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 text-left">Hello {userName},</h1>
            <h2 className="text-2xl font-bold text-gray-900 text-left">How do you want to sign in as</h2>
          </div>

          {/* Role Selection Buttons */}
          <div className="space-y-4">
            {/* Photofish Admin Panel Button */}
            <button
              onClick={() => handleRoleSelection('admin')}
              className="w-full bg-blue-600 text-white py-2.5 rounded-lg font-medium hover:bg-blue-700 transition-colors text-sm"
            >
              Photofish Admin Panel
            </button>

            {/* Regular User Interface Button */}
            <button
              onClick={() => handleRoleSelection('user')}
              className="w-full bg-gray-800 text-white py-2.5 rounded-lg font-medium hover:bg-gray-900 transition-colors text-sm"
            >
              Regular User Interface
            </button>

            {/* Photographer Interface Button */}
            <button
              onClick={() => handleRoleSelection('photographer')}
              className="w-full bg-gray-800 text-white py-2.5 rounded-lg font-medium hover:bg-gray-900 transition-colors text-sm"
            >
              Photographer Interface
            </button>
          </div>

          {/* Support Text */}
          <div className="mt-6 text-sm text-left">
            <p className="text-gray-900 mb-1">If you need further assistance</p>
            <a href="#" className="text-gray-600 hover:underline">
              contact our support team
            </a>
          </div>
        </div>
      </div>

      {/* Right Side - Image/Background */}
      <div className="w-1/2 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <div className="w-32 h-32 bg-gray-400 rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg className="w-16 h-16 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
            </svg>
          </div>
          <p className="text-lg font-medium">Welcome to PhotoFish</p>
          <p className="text-sm">Choose your role to continue</p>
        </div>
      </div>
    </div>
  );
};

export default RoleSelectionScreen;
