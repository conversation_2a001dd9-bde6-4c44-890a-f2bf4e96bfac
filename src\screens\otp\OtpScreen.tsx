import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { AppDispatch } from '../../store/store';
import { verifyOTP } from '../../store/thunks/authThunks';
import { clearError, selectAuth } from '../../store/slices/authSlice';
import photofishLogo from '../../assets/images/photofish-logo.svg';

interface OtpScreenProps {
  onNavigateBack: () => void;
  onVerifyOtp: (otp: string) => void;
  email?: string;
}

const OtpScreen: React.FC<OtpScreenProps> = ({ onNavigateBack, onVerifyOtp, email = "your email" }) => {
  const navigate = useNavigate();
  // Local state (keeping original)
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Redux integration (NEW - added only this)
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, error, isAuthenticated, showRoleSelection, pendingUserId } = useSelector(selectAuth);

  // Clear Redux errors when component mounts (NEW)
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Handle successful verification - navigate appropriately
  useEffect(() => {
    if (isAuthenticated) {
      if (showRoleSelection) {
        navigate('/role-selection');
      } else {
        navigate('/account-success');
      }
    }
  }, [isAuthenticated, showRoleSelection, navigate]);

  // Original input change handler (unchanged)
  const handleInputChange = (index: number, value: string) => {
    if (value.length > 1) return; // Only allow single character
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Original keydown handler (unchanged)
  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    // Handle backspace
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Handle form submission (MODIFIED - added Redux dispatch)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const otpString = otp.join('');
    
    if (otpString.length === 6 && pendingUserId) {
      try {
        await dispatch(verifyOTP({
          userId: pendingUserId,
          otpData: {
            otp: otpString,
            login_mode: 'USER' // Default to USER, can be modified based on your needs
          }
        })).unwrap();
        
        // Success - navigation handled by useEffect above
      } catch (err) {
        // Error handled by Redux state
        console.error('OTP verification failed:', err);
      }
    }
  };

  // Original helper function (unchanged)
  const handleUseAnotherEmail = () => {
    // This would typically navigate back to login/signup
    onNavigateBack();
  };

  // Original validation (unchanged)
  const isOtpComplete = otp.every(digit => digit !== '');

  return (
    <div className="min-h-screen flex bg-gray-100">
      {/* Left Side - OTP Form (ORIGINAL STYLING PRESERVED) */}
      <div className="w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-sm">
          {/* Logo and Back Button (original) */}
          <div className="mb-8 flex items-center justify-between">
            <div className="flex items-center">
              <img
                src={photofishLogo}
                alt="PhotoFish Logo"
                className="h-8 w-auto"
              />
            </div>
            <button
              onClick={onNavigateBack}
              disabled={isLoading} // NEW - disable when loading
              className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-1" />
              <span className="text-sm">Back</span>
            </button>
          </div>

          {/* Verify OTP Header (original) */}
          <h1 className="text-2xl font-bold text-gray-900 mb-8 text-left">Verify OTP</h1>

          {/* Error Message (NEW - Redux error display) */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-300 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Form (original styling) */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* OTP Input Fields (original styling) */}
            <div className="flex justify-between gap-3">
              {otp.map((digit, index) => (
                <input
                  key={index}
                  ref={(el) => { inputRefs.current[index] = el; }}
                  type="text"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleInputChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  disabled={isLoading} // NEW - disable when loading
                  className={`w-12 h-12 text-center text-lg font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent ${
                    isLoading ? 'bg-gray-100 cursor-not-allowed' : ''
                  }`} // NEW - loading styles
                  placeholder="*"
                />
              ))}
            </div>

            {/* Helper Text (original) */}
            <div className="flex items-center justify-between text-sm w-full">
              <span className="text-gray-600 text-left">
                Enter code sent to {email}
              </span>
              <button
                type="button"
                onClick={handleUseAnotherEmail}
                disabled={isLoading} // NEW - disable when loading
                className="text-blue-600 hover:underline text-right"
              >
                Use a different email
              </button>
            </div>

            {/* Verify Button (original styling + loading state) */}
            <button
              type="submit"
              disabled={!isOtpComplete || isLoading || !pendingUserId} // MODIFIED - added loading and userId check
              className={`w-full py-2.5 rounded-lg font-medium transition-colors text-sm ${
                (isOtpComplete && !isLoading && pendingUserId)
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`} // MODIFIED - added loading condition
            >
              {isLoading ? 'Verifying...' : 'Verify'} {/* NEW - loading text */}
            </button>
          </form>

          {/* Missing User ID Warning (NEW - helpful debug info) */}
          {!pendingUserId && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-300 rounded-lg">
              <p className="text-yellow-600 text-sm">
                No user ID found. Please go back and sign up again.
              </p>
            </div>
          )}

          {/* Social Login (ORIGINAL STYLING PRESERVED) */}
          <div className="mt-6">
            <button 
              disabled={isLoading} // NEW - disable when loading
              className={`w-full flex items-center justify-center py-2.5 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors ${
                isLoading ? 'cursor-not-allowed opacity-50' : ''
              }`} // NEW - loading styles
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span className="text-sm font-medium">Or sign in with Google</span>
            </button>
          </div>
        </div>
      </div>

      {/* Right Side - ORIGINAL BLACK PANEL PRESERVED */}
      <div className="w-1/2 pt-12 pr-12 pb-12">
        <div className="bg-black rounded-3xl shadow-2xl h-full flex items-center justify-center">
          <div className="text-gray-400 text-center">
            <div className="text-6xl mb-4">📱</div>
            <p className="text-lg">OTP Verification</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OtpScreen;