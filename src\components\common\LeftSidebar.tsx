import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Calendar,
  DollarSign,
  Package,
  User,
  Users,
  Settings,
  LogOut,
  Heart,
  CreditCard,
  Grid3x3
} from 'lucide-react';
import photofishLogo from '../../assets/images/photofish-logo.svg';

const LeftSidebar: React.FC = () => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path);
  };

  const getLinkClass = (path: string) => {
    return isActive(path)
      ? "flex items-center gap-3 px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg text-left"
      : "flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-lg text-left";
  };

  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center">
          <img
            src={photofishLogo}
            alt="PhotoFish Logo"
            className="h-6 w-auto"
          />
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <div className="space-y-1">
          <Link
            to="/dashboard"
            className={getLinkClass("/dashboard")}
          >
            <LayoutDashboard size={20} />
            Main Dashboard
          </Link>

          <Link 
            to="/events" 
            className={getLinkClass("/events")}
          >
            <Grid3x3 size={20} />
            Events
          </Link>

          <Link 
            to="/gallery" 
            className={getLinkClass("/gallery")}
          >
            <Heart size={20} />
            Photo Gallery
          </Link>

          <Link 
            to="/earnings" 
            className={getLinkClass("/earnings")}
          >
            <DollarSign size={20} />
            Earnings
          </Link>

          <Link 
            to="/product-stock" 
            className={getLinkClass("/product-stock")}
          >
            <Package size={20} />
            Product Stock
          </Link>
        </div>

        {/* Second Section - Separated by space */}
        <div className="mt-12">
          <div className="space-y-1">
            <Link 
              to="/portfolio" 
              className={getLinkClass("/portfolio")}
            >
              <User size={20} />
              Portfolio
            </Link>

            <Link 
              to="/calendar" 
              className={getLinkClass("/calendar")}
            >
              <Calendar size={20} />
              Calendar
            </Link>

            <Link 
              to="/clients" 
              className={getLinkClass("/clients")}
            >
              <Users size={20} />
              Clients
            </Link>

            <Link 
              to="/invoice" 
              className={getLinkClass("/invoice")}
            >
              <CreditCard size={20} />
              Invoice
            </Link>
          </div>
        </div>
      </nav>

      {/* Bottom Section */}
      <div className="p-4 border-t border-gray-200">
        <div className="space-y-1">
          <Link 
            to="/settings" 
            className={getLinkClass("/settings")}
          >
            <Settings size={20} />
            Settings
          </Link>

          <Link 
            to="/login" 
            className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-lg text-left"
          >
            <LogOut size={20} />
            Logout
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LeftSidebar;
