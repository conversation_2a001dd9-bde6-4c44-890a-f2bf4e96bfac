import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { loginUser, signupUser, verifyOTP, forgotPassword, validateToken } from '../thunks/authThunks'
import { isValidToken } from '../../utils/tokenUtils'

export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  full_name: string
  display_name: string
  user_type: 'USER' | 'PHOTOGRAPHER' | 'BOTH'
  account_status: string
  is_email_verified: boolean
  profile_picture: string | null
  admin_access: boolean
  created_at: string
  last_login_at: string | null
}

export interface AuthState {
  user: User | null
  tokens: {
    access: string | null
    refresh: string | null
  }
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  loginMode: 'USER' | 'PHOTOGRAPHER' | null
  showRoleSelection: boolean
  pendingUserId: string | null // For OTP verification
}

const initialState: AuthState = {
  user: null,
  tokens: { access: null, refresh: null },
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginMode: null,
  showRoleSelection: false,
  pendingUserId: null,
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    
    selectRole: (state, action: PayloadAction<'USER' | 'PHOTOGRAPHER' | 'ADMIN'>) => {
      state.loginMode = action.payload === 'ADMIN' ? 'USER' : action.payload
      state.showRoleSelection = false
    },

    logout: (state) => {
      return initialState
    },

    // Check token validity and update authentication status
    checkTokenValidity: (state) => {
      console.log('checkTokenValidity - Checking token validity')
      console.log('checkTokenValidity - Current tokens:', !!state.tokens?.access)

      if (state.tokens?.access) {
        const isValid = isValidToken(state.tokens.access)
        console.log('checkTokenValidity - Token is valid:', isValid)

        if (!isValid) {
          // Token is expired, clear authentication
          console.log('checkTokenValidity - Token expired, clearing auth state')
          return initialState
        } else {
          // Token is valid, ensure authentication state is correct
          console.log('checkTokenValidity - Token valid, ensuring auth state is correct')
          state.isAuthenticated = true
        }
      } else {
        // No token, clear authentication
        console.log('checkTokenValidity - No token found, clearing auth state')
        return initialState
      }
    },
  },
  extraReducers: (builder) => {
    // Login User
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        console.log('🔐 Login successful - Processing response:', action.payload)
        console.log('🔐 Response keys:', Object.keys(action.payload))
        console.log('🔐 Full response structure:', JSON.stringify(action.payload, null, 2))

        // Try different possible response structures
        let access, refresh, current_login_mode, admin_access, userData

        // Check if tokens are directly in the response
        if (action.payload.access && action.payload.refresh) {
          ({ access, refresh, current_login_mode, admin_access, ...userData } = action.payload)
        }
        // Check if tokens are nested in a 'data' property
        else if (action.payload.data && action.payload.data.access) {
          ({ access, refresh, current_login_mode, admin_access, ...userData } = action.payload.data)
        }
        // Check if tokens are nested in a 'tokens' property
        else if (action.payload.tokens) {
          access = action.payload.tokens.access
          refresh = action.payload.tokens.refresh
          current_login_mode = action.payload.current_login_mode
          admin_access = action.payload.admin_access
          userData = { ...action.payload }
          delete userData.tokens
        }
        // Check if using different token names (access_token, refresh_token)
        else if (action.payload.access_token) {
          access = action.payload.access_token
          refresh = action.payload.refresh_token
          current_login_mode = action.payload.current_login_mode
          admin_access = action.payload.admin_access
          userData = { ...action.payload }
        }
        else {
          console.error('🔐 Unknown response structure - tokens not found!')
          access = null
          refresh = null
          current_login_mode = action.payload.current_login_mode
          admin_access = action.payload.admin_access
          userData = { ...action.payload }
        }

        console.log('🔐 Extracted tokens:', {
          hasAccess: !!access,
          hasRefresh: !!refresh,
          accessPreview: access ? access.substring(0, 50) + '...' : 'null',
          refreshPreview: refresh ? refresh.substring(0, 50) + '...' : 'null'
        })

        state.user = { ...userData, admin_access: admin_access || false }
        state.tokens = { access, refresh }
        state.isAuthenticated = true
        state.isLoading = false
        state.error = null
        state.loginMode = current_login_mode || null

        console.log('🔐 Updated auth state:', {
          isAuthenticated: state.isAuthenticated,
          hasTokens: !!(state.tokens?.access && state.tokens?.refresh),
          userId: state.user?.id
        })

        // Show role selection if user is admin logging in as USER
        state.showRoleSelection =
          current_login_mode === 'USER' &&
          (admin_access === true)
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // Signup User
    builder
      .addCase(signupUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(signupUser.fulfilled, (state, action) => {
        state.isLoading = false
        state.error = null
        
        // Extract user ID from backend response format: { user: { id, ... } }
        const userId = action.payload.user?.id || action.payload.id || action.payload.user_id
        
        if (userId) {
          state.pendingUserId = userId
        }
        
        // If the backend sends tokens immediately (for auto-login after signup)
        if (action.payload.access && action.payload.refresh) {
          state.tokens = {
            access: action.payload.access,
            refresh: action.payload.refresh
          }
          // Don't set isAuthenticated yet - wait for email verification
        }
      })
      .addCase(signupUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // Verify OTP
    builder
      .addCase(verifyOTP.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        const { user, tokens, admin_access } = action.payload
        
        state.user = { ...user, admin_access: admin_access || false }
        state.tokens = tokens
        state.isAuthenticated = true
        state.isLoading = false
        state.error = null
        state.pendingUserId = null
        
        // Handle role selection for admin users
        state.showRoleSelection = admin_access === true
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // Forgot Password
    builder
      .addCase(forgotPassword.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(forgotPassword.fulfilled, (state) => {
        state.isLoading = false
        state.error = null
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })

    // Token Validation
    builder
      .addCase(validateToken.fulfilled, (state) => {
        // Token is valid, no action needed
      })
      .addCase(validateToken.rejected, (state) => {
        // Token is invalid or expired, clear authentication
        return initialState
      })
  },
})

export const { clearError, selectRole, logout, checkTokenValidity } = authSlice.actions
export default authSlice.reducer

// Properly typed selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth
export const selectUser = (state: { auth: AuthState }) => state.auth.user
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated
export const selectIsLoading = (state: { auth: AuthState }) => state.auth.isLoading
export const selectError = (state: { auth: AuthState }) => state.auth.error
export const selectShowRoleSelection = (state: { auth: AuthState }) => state.auth.showRoleSelection